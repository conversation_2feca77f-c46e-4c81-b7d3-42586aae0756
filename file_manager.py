#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件管理器模块
负责文件重命名、归类、备份等操作，包含文件锁机制
"""

import os
import re
import shutil
import logging
import threading
from typing import Dict, List, Optional, Tuple
from pathlib import Path
from datetime import datetime
from collections import defaultdict
import json
try:
    import fcntl
except ImportError:
    # Windows系统不支持fcntl，使用替代方案
    fcntl = None
import tempfile

from config import config_manager
from ai_analyzer import AnalysisResult


class FileLock:
    """文件锁实现（跨平台）"""

    def __init__(self, file_path: str):
        self.file_path = Path(file_path)
        self.lock_file = self.file_path.with_suffix(self.file_path.suffix + '.lock')
        self.lock_fd = None

    def __enter__(self):
        try:
            self.lock_fd = open(self.lock_file, 'w')
            if fcntl:  # Unix/Linux系统
                fcntl.flock(self.lock_fd.fileno(), fcntl.LOCK_EX | fcntl.LOCK_NB)
            # Windows系统使用文件存在性作为锁机制
            return self
        except (IOError, OSError) as e:
            if self.lock_fd:
                self.lock_fd.close()
            raise Exception(f"无法获取文件锁 {self.file_path}: {e}")

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.lock_fd:
            if fcntl:  # Unix/Linux系统
                fcntl.flock(self.lock_fd.fileno(), fcntl.LOCK_UN)
            self.lock_fd.close()
            try:
                self.lock_file.unlink()
            except FileNotFoundError:
                pass


class FileManager:
    """文件管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.config = config_manager.processing
        self._lock = threading.Lock()
    
    def clean_filename(self, text: str) -> str:
        """清理文件名，移除非法字符"""
        # 移除或替换非法字符
        illegal_chars = r'[<>:"/\\|?*]'
        cleaned = re.sub(illegal_chars, '_', text)
        
        # 限制长度
        max_length = 100
        if len(cleaned) > max_length:
            cleaned = cleaned[:max_length]
        
        # 移除首尾空格和点
        cleaned = cleaned.strip('. ')
        
        return cleaned if cleaned else "unknown"
    
    def create_backup(self, file_mappings: Dict[str, Dict[str, str]]) -> Optional[str]:
        """创建文件备份"""
        if not self.config.backup_enabled:
            return None
        
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_dir = Path(f"backup_{timestamp}")
            backup_dir.mkdir(exist_ok=True)
            
            backup_info = {
                'timestamp': timestamp,
                'files': []
            }
            
            for amount, files in file_mappings.items():
                for file_type, file_path in files.items():
                    if Path(file_path).exists():
                        backup_path = backup_dir / Path(file_path).name
                        shutil.copy2(file_path, backup_path)
                        backup_info['files'].append({
                            'original': file_path,
                            'backup': str(backup_path),
                            'amount': amount,
                            'type': file_type
                        })
            
            # 保存备份信息
            with open(backup_dir / 'backup_info.json', 'w', encoding='utf-8') as f:
                json.dump(backup_info, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"备份创建成功: {backup_dir}")
            return str(backup_dir)
            
        except Exception as e:
            self.logger.error(f"创建备份失败: {e}")
            return None
    
    def execute_rename_with_lock(self, file_mappings: Dict[str, Dict[str, str]], 
                                ai_results: Dict[str, AnalysisResult]) -> Tuple[int, List[str]]:
        """执行文件重命名（带文件锁）"""
        if not file_mappings:
            self.logger.warning("没有文件需要重命名")
            return 0, ["没有文件需要重命名"]
        
        # 创建备份
        backup_dir = self.create_backup(file_mappings)
        
        success_count = 0
        errors = []
        
        for amount, files in file_mappings.items():
            if amount not in ai_results:
                errors.append(f"金额 {amount} 没有对应的AI分析结果")
                continue
            
            result = ai_results[amount]
            if not result.destination:
                errors.append(f"金额 {amount} 的AI分析失败: {result.error}")
                continue
            
            try:
                # 清理目标文件名
                clean_destination = self.clean_filename(result.destination)
                
                # 生成新文件名
                new_itinerary_name = f"{amount}-{clean_destination}-行程单.pdf"
                new_invoice_name = f"{amount}-{clean_destination}-发票.pdf"
                
                # 获取目录路径
                base_dir = Path(files['itinerary_original']).parent
                new_itinerary_path = base_dir / new_itinerary_name
                new_invoice_path = base_dir / new_invoice_name
                
                # 检查目标文件是否已存在
                if new_itinerary_path.exists() or new_invoice_path.exists():
                    errors.append(f"目标文件已存在: {amount} - {clean_destination}")
                    continue
                
                # 使用文件锁进行重命名
                with FileLock(files['itinerary_original']):
                    with FileLock(files['invoice_original']):
                        # 验证源文件仍然存在
                        if not Path(files['itinerary_original']).exists():
                            errors.append(f"源文件不存在: {files['itinerary_original']}")
                            continue
                        
                        if not Path(files['invoice_original']).exists():
                            errors.append(f"源文件不存在: {files['invoice_original']}")
                            continue
                        
                        # 执行重命名
                        os.rename(files['itinerary_original'], new_itinerary_path)
                        os.rename(files['invoice_original'], new_invoice_path)
                        
                        success_count += 2
                        self.logger.info(f"重命名成功: {amount} - {clean_destination}")
                        
            except Exception as e:
                error_msg = f"重命名失败 (金额: {amount}): {str(e)}"
                errors.append(error_msg)
                self.logger.error(error_msg)
        
        self.logger.info(f"重命名完成！成功处理 {success_count} 个文件")
        return success_count, errors
    
    def extract_shop_name(self, destination: str) -> str:
        """从终点信息中提取店铺名称"""
        if not destination:
            return "其他地点"
        
        # 处理包含分隔符的情况
        separators = ['|', '→', '-', '—']
        for sep in separators:
            if sep in destination:
                parts = destination.split(sep)
                if len(parts) >= 2:
                    shop_name = parts[1].strip()
                    if shop_name:
                        return self.clean_filename(shop_name)
        
        # 如果没有分隔符，直接使用整个字符串
        return self.clean_filename(destination.strip())
    
    def scan_renamed_files(self, folder_path: str) -> Dict[str, List[Dict]]:
        """扫描已重命名的文件"""
        folder_path = Path(folder_path)
        itinerary_pattern = "*-行程单.pdf"
        itinerary_files = sorted(folder_path.glob(itinerary_pattern))
        
        if not itinerary_files:
            self.logger.warning("未找到已重命名的行程单文件")
            return {}
        
        self.logger.info(f"找到 {len(itinerary_files)} 个行程单文件")
        
        shop_data = defaultdict(list)
        
        for itinerary_file in itinerary_files:
            filename = itinerary_file.name
            match = re.match(r'^(\d+\.?\d*)-(.+)-行程单\.pdf$', filename)
            
            if match:
                amount = match.group(1)
                destination = match.group(2)
                shop_name = self.extract_shop_name(destination)
                
                invoice_file = folder_path / f"{amount}-{destination}-发票.pdf"
                
                if invoice_file.exists():
                    shop_data[shop_name].append({
                        'amount': float(amount),
                        'destination': destination,
                        'itinerary_file': str(itinerary_file),
                        'invoice_file': str(invoice_file)
                    })
                else:
                    self.logger.warning(f"未找到对应的发票文件: {invoice_file.name}")
        
        self.logger.info(f"成功分析 {len(shop_data)} 个不同店铺的文件")
        return dict(shop_data)
    
    def create_shop_classification(self, folder_path: str, 
                                 shop_data: Dict[str, List[Dict]]) -> Tuple[bool, List[str]]:
        """创建店铺归类"""
        if not shop_data:
            return False, ["没有数据可归类"]
        
        folder_path = Path(folder_path)
        classification_folder = folder_path / "按店铺归类"
        
        try:
            classification_folder.mkdir(exist_ok=True)
            
            success_count = 0
            errors = []
            
            for shop_name, files in shop_data.items():
                try:
                    clean_shop_name = self.clean_filename(shop_name)
                    shop_folder = classification_folder / clean_shop_name
                    shop_folder.mkdir(exist_ok=True)
                    
                    for file_info in files:
                        # 移动文件而不是复制
                        itinerary_src = Path(file_info['itinerary_file'])
                        invoice_src = Path(file_info['invoice_file'])
                        
                        itinerary_dest = shop_folder / itinerary_src.name
                        invoice_dest = shop_folder / invoice_src.name
                        
                        # 使用文件锁
                        with FileLock(str(itinerary_src)):
                            with FileLock(str(invoice_src)):
                                if itinerary_src.exists():
                                    shutil.move(str(itinerary_src), str(itinerary_dest))
                                if invoice_src.exists():
                                    shutil.move(str(invoice_src), str(invoice_dest))
                    
                    success_count += 1
                    self.logger.info(f"处理店铺: {shop_name} ({len(files)} 个文件)")
                    
                except Exception as e:
                    error_msg = f"处理店铺失败: {shop_name} - {str(e)}"
                    errors.append(error_msg)
                    self.logger.error(error_msg)
            
            # 生成统计报告
            self._generate_classification_report(classification_folder, shop_data)
            
            self.logger.info(f"店铺归类完成！成功处理 {success_count} 个店铺")
            return True, errors
            
        except Exception as e:
            error_msg = f"创建店铺归类失败: {str(e)}"
            self.logger.error(error_msg)
            return False, [error_msg]
    
    def _generate_classification_report(self, classification_folder: Path, 
                                      shop_data: Dict[str, List[Dict]]):
        """生成归类统计报告"""
        try:
            report_file = classification_folder / "归类统计报告.txt"
            
            total_amount = sum(
                sum(file_info['amount'] for file_info in files)
                for files in shop_data.values()
            )
            total_trips = sum(len(files) for files in shop_data.values())
            
            sorted_shops = sorted(
                shop_data.items(),
                key=lambda x: sum(file_info['amount'] for file_info in x[1]),
                reverse=True
            )
            
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("=" * 80 + "\n")
                f.write("PDF智能管理器 - 店铺归类统计报告\n")
                f.write("=" * 80 + "\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"总店铺数量: {len(shop_data)} 个\n")
                f.write(f"总行程数量: {total_trips} 个\n")
                f.write(f"总金额: ¥{total_amount:.2f}\n")
                f.write("=" * 80 + "\n\n")
                
                for i, (shop_name, files) in enumerate(sorted_shops, 1):
                    shop_amount = sum(file_info['amount'] for file_info in files)
                    f.write(f"{i:2d}. 【{shop_name}】\n")
                    f.write(f"    行程数量: {len(files)} 个\n")
                    f.write(f"    总金额: ¥{shop_amount:.2f}\n")
                    f.write(f"    详细清单:\n")
                    
                    for file_info in sorted(files, key=lambda x: x['amount']):
                        f.write(f"      - ¥{file_info['amount']:>8.2f} | {file_info['destination']}\n")
                    f.write(f"\n")
                
                f.write(f"=" * 80 + "\n")
                f.write(f"使用说明:\n")
                f.write(f"   1. 每个店铺的文件都在对应的文件夹中\n")
                f.write(f"   2. 可以按店铺文件夹分别提交报销申请\n")
                f.write(f"   3. 每个文件夹包含该店铺的所有行程单和发票\n")
                f.write(f"   4. 原文件已移动到对应店铺文件夹\n")
            
            self.logger.info(f"统计报告生成成功: {report_file}")
            
        except Exception as e:
            self.logger.error(f"生成统计报告失败: {e}")
    
    def get_file_status(self, folder_path: str) -> Dict:
        """获取文件状态"""
        folder_path = Path(folder_path)
        
        original_files = list(folder_path.glob("*_行程单.pdf"))
        renamed_files = list(folder_path.glob("*-行程单.pdf"))
        classification_folder = folder_path / "按店铺归类"
        
        return {
            'original_count': len(original_files),
            'renamed_count': len(renamed_files),
            'has_classification': classification_folder.exists(),
            'status': 'original' if original_files else ('renamed' if renamed_files else 'empty'),
            'classification_folder': str(classification_folder) if classification_folder.exists() else None
        }
