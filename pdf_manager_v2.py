#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF智能管理器 v2.0 - 重构版本
功能：PDF提取 → AI分析 → 智能重命名 → 店铺归类
特性：模块化架构、并发处理、错误重试、文件锁、进度显示
"""

import os
import sys
import logging
from pathlib import Path
from typing import Optional

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from config import config_manager, prompt_manager
from pdf_extractor import PDFExtractor
from ai_analyzer import AIAnalyzer
from file_manager import FileManager
from ui_manager import UIManager


class PDFManagerV2:
    """PDF管理器主类 - 重构版本"""
    
    def __init__(self):
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # 初始化各个模块
        self.pdf_extractor = PDFExtractor()
        self.ai_analyzer = AIAnalyzer()
        self.file_manager = FileManager()
        self.ui_manager = UIManager()
        
        self.current_folder = None
        
        # 验证配置
        if not config_manager.validate_config():
            self.logger.error("配置验证失败")
            sys.exit(1)
    
    def setup_logging(self):
        """设置日志系统"""
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        logging.basicConfig(
            level=logging.INFO,
            format=log_format,
            handlers=[
                logging.FileHandler('pdf_manager.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
        # 设置第三方库日志级别
        logging.getLogger('requests').setLevel(logging.WARNING)
        logging.getLogger('urllib3').setLevel(logging.WARNING)
    
    def select_folder(self) -> bool:
        """选择工作文件夹"""
        pdf_folders = self.pdf_extractor.find_pdf_folders()
        selected_folder = self.ui_manager.show_folder_selection(pdf_folders)
        
        if not selected_folder:
            return False
        
        self.current_folder = selected_folder
        self.logger.info(f"选择工作文件夹: {self.current_folder}")
        return True
    
    def full_process(self):
        """完整流程：提取→分析→重命名→归类"""
        self.logger.info("开始完整流程")
        
        try:
            # 1. 提取PDF内容
            print("\n🔍 开始提取PDF内容...")
            pdf_files = self.pdf_extractor.extract_pdf_content_parallel(self.current_folder)
            
            if not pdf_files:
                print("❌ 未找到可处理的PDF文件")
                return
            
            # 显示提取统计
            stats = self.pdf_extractor.get_pdf_statistics(self.current_folder)
            self.ui_manager.show_statistics(stats)
            
            # 2. AI分析
            print("\n🤖 开始AI分析终点信息...")
            ai_results = self.ai_analyzer.analyze_destinations_parallel(pdf_files)
            
            if not ai_results:
                print("❌ AI分析失败，无法继续")
                return
            
            # 显示分析统计
            analysis_stats = self.ai_analyzer.get_analysis_statistics(ai_results)
            self.ui_manager.show_statistics(analysis_stats)
            
            # 3. 确认重命名
            if not self.ui_manager.confirm_operation("确认执行重命名操作？"):
                print("❌ 用户取消重命名操作")
                return
            
            # 4. 执行重命名
            print("\n📝 开始执行文件重命名...")
            file_mappings = self.pdf_extractor.get_file_mappings(self.current_folder)
            success_count, errors = self.file_manager.execute_rename_with_lock(file_mappings, ai_results)
            
            if errors:
                self.ui_manager.show_errors(errors)
            
            if success_count == 0:
                print("❌ 重命名失败，无法继续")
                return
            
            print(f"✅ 重命名完成！成功处理 {success_count} 个文件")
            
            # 5. 扫描重命名后的文件
            print("\n📂 扫描已重命名的文件...")
            shop_data = self.file_manager.scan_renamed_files(self.current_folder)
            
            if not shop_data:
                print("❌ 未找到可归类的文件")
                return
            
            # 6. 确认店铺归类
            if not self.ui_manager.confirm_operation("确认执行店铺归类？"):
                print("⏭️  跳过店铺归类")
                return
            
            # 7. 执行店铺归类
            print("\n🏪 开始店铺归类...")
            success, errors = self.file_manager.create_shop_classification(self.current_folder, shop_data)
            
            if errors:
                self.ui_manager.show_errors(errors)
            
            if success:
                print("🎉 完整流程完成！")
            else:
                print("⚠️  店铺归类部分失败")
                
        except Exception as e:
            self.logger.error(f"完整流程执行失败: {e}")
            print(f"❌ 执行失败: {e}")
    
    def rename_only(self):
        """仅重命名流程"""
        self.logger.info("开始重命名流程")
        
        try:
            # 提取PDF内容
            print("\n🔍 开始提取PDF内容...")
            pdf_files = self.pdf_extractor.extract_pdf_content_parallel(self.current_folder)
            
            if not pdf_files:
                print("❌ 未找到可处理的PDF文件")
                return
            
            # AI分析
            print("\n🤖 开始AI分析终点信息...")
            ai_results = self.ai_analyzer.analyze_destinations_parallel(pdf_files)
            
            if not ai_results:
                print("❌ AI分析失败")
                return
            
            # 确认并执行重命名
            if self.ui_manager.confirm_operation("确认执行重命名操作？"):
                print("\n📝 开始执行文件重命名...")
                file_mappings = self.pdf_extractor.get_file_mappings(self.current_folder)
                success_count, errors = self.file_manager.execute_rename_with_lock(file_mappings, ai_results)
                
                if errors:
                    self.ui_manager.show_errors(errors)
                
                print(f"✅ 重命名完成！成功处理 {success_count} 个文件")
            else:
                print("❌ 用户取消操作")
                
        except Exception as e:
            self.logger.error(f"重命名流程执行失败: {e}")
            print(f"❌ 执行失败: {e}")
    
    def classify_only(self):
        """仅店铺归类流程"""
        self.logger.info("开始店铺归类流程")
        
        try:
            print("\n📂 扫描已重命名的文件...")
            shop_data = self.file_manager.scan_renamed_files(self.current_folder)
            
            if not shop_data:
                print("❌ 未找到已重命名的文件")
                return
            
            if self.ui_manager.confirm_operation("确认执行店铺归类？"):
                print("\n🏪 开始店铺归类...")
                success, errors = self.file_manager.create_shop_classification(self.current_folder, shop_data)
                
                if errors:
                    self.ui_manager.show_errors(errors)
                
                if success:
                    print("✅ 店铺归类完成！")
                else:
                    print("❌ 店铺归类失败")
            else:
                print("❌ 用户取消操作")
                
        except Exception as e:
            self.logger.error(f"店铺归类流程执行失败: {e}")
            print(f"❌ 执行失败: {e}")
    
    def show_status(self):
        """显示文件状态"""
        try:
            status = self.file_manager.get_file_status(self.current_folder)
            self.ui_manager.show_file_status(status)
            
            # 显示详细统计
            stats = self.pdf_extractor.get_pdf_statistics(self.current_folder)
            self.ui_manager.show_statistics(stats)
            
        except Exception as e:
            self.logger.error(f"获取状态失败: {e}")
            print(f"❌ 获取状态失败: {e}")
    
    def config_management(self):
        """配置管理"""
        while True:
            choice = self.ui_manager.show_config_menu()
            
            if choice == '0':
                break
            elif choice == '1':
                self._manage_api_config()
            elif choice == '2':
                self._manage_processing_config()
            elif choice == '3':
                self._manage_ui_config()
            elif choice == '4':
                self._manage_prompts()
            elif choice == '5':
                if config_manager.save_config():
                    print("✅ 配置保存成功")
                else:
                    print("❌ 配置保存失败")
            elif choice == '6':
                if self.ui_manager.confirm_operation("确认重置为默认配置？", False):
                    # 这里可以实现重置逻辑
                    print("🔄 配置重置功能待实现")
    
    def _manage_api_config(self):
        """管理API配置"""
        api_config = config_manager.api
        self.ui_manager.show_api_config(api_config)
        
        if self.ui_manager.confirm_operation("是否修改API配置？", False):
            new_url = self.ui_manager.input_with_default("API URL", api_config.url)
            new_key = self.ui_manager.input_with_default("API密钥", "")
            new_model = self.ui_manager.input_with_default("模型名称", api_config.model)
            
            config_manager.update_api_config(
                url=new_url,
                key=new_key if new_key else api_config.key,
                model=new_model
            )
            print("✅ API配置已更新")
    
    def _manage_processing_config(self):
        """管理处理配置"""
        proc_config = config_manager.processing
        self.ui_manager.show_processing_config(proc_config)
        
        if self.ui_manager.confirm_operation("是否修改性能配置？", False):
            try:
                new_workers = int(self.ui_manager.input_with_default(
                    "最大并发数", str(proc_config.max_workers)
                ))
                new_batch_size = int(self.ui_manager.input_with_default(
                    "批处理大小", str(proc_config.batch_size)
                ))
                
                config_manager.update_processing_config(
                    max_workers=new_workers,
                    batch_size=new_batch_size
                )
                print("✅ 性能配置已更新")
            except ValueError:
                print("❌ 输入格式错误")
    
    def _manage_ui_config(self):
        """管理UI配置"""
        print("🎨 UI配置管理功能待实现")
    
    def _manage_prompts(self):
        """管理提示词"""
        print("📝 提示词管理功能待实现")
    
    def run(self):
        """运行主程序"""
        try:
            self.ui_manager.show_banner()
            
            # 选择工作文件夹
            if not self.select_folder():
                print("👋 程序退出")
                return
            
            # 主循环
            while True:
                choice = self.ui_manager.show_main_menu()
                
                if choice == '0':
                    print("👋 再见！")
                    break
                elif choice == '1':
                    self.full_process()
                elif choice == '2':
                    self.rename_only()
                elif choice == '3':
                    self.classify_only()
                elif choice == '4':
                    self.show_status()
                elif choice == '5':
                    self.config_management()
                
                self.ui_manager.wait_for_enter()
                
        except KeyboardInterrupt:
            print("\n👋 用户中断程序")
        except Exception as e:
            self.logger.error(f"程序运行异常: {e}")
            print(f"❌ 程序异常: {e}")


def main():
    """主函数"""
    manager = PDFManagerV2()
    manager.run()


if __name__ == "__main__":
    main()
