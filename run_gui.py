#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF智能管理器 GUI版本启动脚本
"""

import sys
import tkinter as tk
from tkinter import messagebox
from pathlib import Path

def check_dependencies():
    """检查依赖项"""
    missing_deps = []
    
    try:
        import requests
    except ImportError:
        missing_deps.append("requests")
    
    try:
        import PyPDF2
    except ImportError:
        missing_deps.append("PyPDF2")
    
    if missing_deps:
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror(
            "依赖缺失", 
            f"缺少以下依赖包：{', '.join(missing_deps)}\n\n"
            f"请运行以下命令安装：\n"
            f"pip install {' '.join(missing_deps)}"
        )
        return False
    
    return True

def check_config():
    """检查配置文件"""
    config_file = Path("config.json")
    if not config_file.exists():
        root = tk.Tk()
        root.withdraw()
        result = messagebox.askyesno(
            "配置文件缺失",
            "未找到配置文件 config.json\n\n"
            "是否创建默认配置文件？"
        )
        
        if result:
            # 创建默认配置
            default_config = {
                "api": {
                    "url": "https://api.siliconflow.cn/v1/chat/completions",
                    "key": "",
                    "model": "deepseek-ai/DeepSeek-V3",
                    "timeout": 60,
                    "max_retries": 3,
                    "retry_delay": 1.0
                },
                "processing": {
                    "max_workers": 4,
                    "batch_size": 10,
                    "enable_parallel": True,
                    "backup_enabled": True
                },
                "ui": {
                    "language": "zh",
                    "theme": "default",
                    "show_progress": True
                }
            }
            
            import json
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, indent=2, ensure_ascii=False)
            
            messagebox.showinfo(
                "配置创建成功",
                "已创建默认配置文件 config.json\n\n"
                "请在配置文件中设置您的API密钥后重新启动程序。"
            )
            return False
        else:
            return False
    
    return True

def main():
    """主函数"""
    print("🚀 启动PDF智能管理器GUI版本...")
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 检查配置
    if not check_config():
        return
    
    try:
        # 导入并启动GUI应用
        from gui_app import PDFManagerGUI
        
        app = PDFManagerGUI()
        app.run()
        
    except ImportError as e:
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror(
            "导入错误",
            f"导入模块失败: {str(e)}\n\n"
            "请确保所有必需的文件都在当前目录中。"
        )
    except Exception as e:
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror(
            "启动错误",
            f"应用启动失败: {str(e)}\n\n"
            "请检查配置文件和依赖项。"
        )

if __name__ == "__main__":
    main()
