#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF智能管理器 - 简洁简约GUI版本
极简设计：白色背景、清晰层次、大量留白
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import queue
import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from config import config_manager
from pdf_extractor import PDFExtractor
from ai_analyzer import AIAnalyzer
from file_manager import FileManager


class SimpleTheme:
    """简约主题配置"""
    
    # 极简配色
    COLORS = {
        'bg': '#FFFFFF',           # 纯白背景
        'text': '#333333',         # 深灰文字
        'text_light': '#666666',   # 浅灰文字
        'text_muted': '#999999',   # 弱化文字
        'accent': '#007AFF',       # iOS蓝强调色
        'accent_hover': '#0056CC', # 强调色悬停
        'border': '#E5E5E5',       # 浅灰边框
        'success': '#34C759',      # 成功绿
        'error': '#FF3B30',        # 错误红
    }
    
    # 简洁字体
    FONTS = {
        'title': ('SF Pro Display', 24, 'normal'),      # 标题
        'heading': ('SF Pro Display', 16, 'normal'),    # 小标题
        'body': ('SF Pro Text', 13, 'normal'),          # 正文
        'caption': ('SF Pro Text', 11, 'normal'),       # 说明文字
        'button': ('SF Pro Text', 13, 'normal'),        # 按钮文字
    }
    
    # 间距系统
    SPACING = {
        'xs': 8, 'sm': 16, 'md': 24, 'lg': 32, 'xl': 48
    }


class SimpleButton(tk.Button):
    """简约按钮"""
    
    def __init__(self, parent, text="", command=None, variant='default', **kwargs):
        self.variant = variant
        
        # 根据变体设置颜色
        if variant == 'primary':
            bg = SimpleTheme.COLORS['accent']
            fg = '#FFFFFF'
            hover_bg = SimpleTheme.COLORS['accent_hover']
        else:
            bg = SimpleTheme.COLORS['bg']
            fg = SimpleTheme.COLORS['text']
            hover_bg = SimpleTheme.COLORS['border']
        
        super().__init__(
            parent,
            text=text,
            command=command,
            font=SimpleTheme.FONTS['button'],
            bg=bg,
            fg=fg,
            activebackground=hover_bg,
            activeforeground=fg,
            relief='flat',
            bd=0,
            cursor='hand2',
            padx=SimpleTheme.SPACING['md'],
            pady=SimpleTheme.SPACING['sm'],
            **kwargs
        )
        
        # 悬停效果
        self.bind('<Enter>', lambda e: self.configure(bg=hover_bg))
        self.bind('<Leave>', lambda e: self.configure(bg=bg))


class SimplePDFManagerGUI:
    """简约PDF管理器GUI"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.setup_ui()
        
        # 业务逻辑组件
        self.pdf_extractor = PDFExtractor()
        self.ai_analyzer = AIAnalyzer()
        self.file_manager = FileManager()
        
        # 状态变量
        self.current_folder = None
        self.processing = False
        
        # 消息队列
        self.message_queue = queue.Queue()
        self.check_queue()
    
    def setup_window(self):
        """设置窗口"""
        self.root.title("PDF智能管理器")
        self.root.geometry("800x600")
        self.root.minsize(700, 500)
        self.root.configure(bg=SimpleTheme.COLORS['bg'])
        
        # 居中显示
        self.center_window()
    
    def center_window(self):
        """窗口居中"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def setup_ui(self):
        """设置用户界面"""
        # 主容器 - 大量留白
        main_container = tk.Frame(
            self.root,
            bg=SimpleTheme.COLORS['bg'],
            padx=SimpleTheme.SPACING['xl'],
            pady=SimpleTheme.SPACING['xl']
        )
        main_container.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        self.setup_title(main_container)
        
        # 文件选择区域
        self.setup_file_selection(main_container)
        
        # 操作按钮区域
        self.setup_actions(main_container)
        
        # 状态显示区域
        self.setup_status(main_container)
        
        # 日志区域
        self.setup_log(main_container)
    
    def setup_title(self, parent):
        """设置标题"""
        title_frame = tk.Frame(parent, bg=SimpleTheme.COLORS['bg'])
        title_frame.pack(fill=tk.X, pady=(0, SimpleTheme.SPACING['xl']))
        
        # 主标题
        title_label = tk.Label(
            title_frame,
            text="PDF智能管理器",
            font=SimpleTheme.FONTS['title'],
            fg=SimpleTheme.COLORS['text'],
            bg=SimpleTheme.COLORS['bg']
        )
        title_label.pack()
        
        # 副标题
        subtitle_label = tk.Label(
            title_frame,
            text="提取 · 分析 · 重命名 · 归类",
            font=SimpleTheme.FONTS['caption'],
            fg=SimpleTheme.COLORS['text_light'],
            bg=SimpleTheme.COLORS['bg']
        )
        subtitle_label.pack(pady=(SimpleTheme.SPACING['xs'], 0))
    
    def setup_file_selection(self, parent):
        """设置文件选择区域"""
        # 区域容器
        file_frame = tk.Frame(parent, bg=SimpleTheme.COLORS['bg'])
        file_frame.pack(fill=tk.X, pady=(0, SimpleTheme.SPACING['lg']))
        
        # 选择按钮
        select_btn = SimpleButton(
            file_frame,
            text="选择文件夹",
            command=self.select_folder,
            variant='primary'
        )
        select_btn.pack()
        
        # 当前文件夹显示
        self.folder_label = tk.Label(
            file_frame,
            text="未选择文件夹",
            font=SimpleTheme.FONTS['body'],
            fg=SimpleTheme.COLORS['text_muted'],
            bg=SimpleTheme.COLORS['bg'],
            wraplength=600
        )
        self.folder_label.pack(pady=(SimpleTheme.SPACING['sm'], 0))
    
    def setup_actions(self, parent):
        """设置操作区域"""
        # 分割线
        separator = tk.Frame(
            parent,
            height=1,
            bg=SimpleTheme.COLORS['border']
        )
        separator.pack(fill=tk.X, pady=SimpleTheme.SPACING['lg'])
        
        # 按钮容器
        actions_frame = tk.Frame(parent, bg=SimpleTheme.COLORS['bg'])
        actions_frame.pack(fill=tk.X, pady=(0, SimpleTheme.SPACING['lg']))
        
        # 按钮配置
        buttons = [
            ("完整流程", self.full_process),
            ("仅重命名", self.rename_only),
            ("仅归类", self.classify_only),
            ("查看状态", self.show_status)
        ]
        
        # 创建按钮
        self.action_buttons = []
        for text, command in buttons:
            btn = SimpleButton(
                actions_frame,
                text=text,
                command=command
            )
            btn.pack(side=tk.LEFT, padx=(0, SimpleTheme.SPACING['sm']))
            self.action_buttons.append(btn)
        
        # 初始禁用按钮
        self.enable_buttons(False)
    
    def setup_status(self, parent):
        """设置状态区域"""
        # 分割线
        separator = tk.Frame(
            parent,
            height=1,
            bg=SimpleTheme.COLORS['border']
        )
        separator.pack(fill=tk.X, pady=(0, SimpleTheme.SPACING['lg']))
        
        # 状态容器
        status_frame = tk.Frame(parent, bg=SimpleTheme.COLORS['bg'])
        status_frame.pack(fill=tk.X, pady=(0, SimpleTheme.SPACING['lg']))
        
        # 状态文字
        self.status_label = tk.Label(
            status_frame,
            text="就绪",
            font=SimpleTheme.FONTS['body'],
            fg=SimpleTheme.COLORS['text'],
            bg=SimpleTheme.COLORS['bg']
        )
        self.status_label.pack(side=tk.LEFT)
        
        # 进度条
        self.progress = ttk.Progressbar(
            status_frame,
            mode='determinate',
            length=200
        )
        self.progress.pack(side=tk.RIGHT)
    
    def setup_log(self, parent):
        """设置日志区域"""
        # 分割线
        separator = tk.Frame(
            parent,
            height=1,
            bg=SimpleTheme.COLORS['border']
        )
        separator.pack(fill=tk.X, pady=(0, SimpleTheme.SPACING['lg']))
        
        # 日志标题
        log_title = tk.Label(
            parent,
            text="处理日志",
            font=SimpleTheme.FONTS['heading'],
            fg=SimpleTheme.COLORS['text'],
            bg=SimpleTheme.COLORS['bg']
        )
        log_title.pack(anchor='w', pady=(0, SimpleTheme.SPACING['sm']))
        
        # 日志文本框
        self.log_text = tk.Text(
            parent,
            height=8,
            font=('Monaco', 11),
            bg=SimpleTheme.COLORS['bg'],
            fg=SimpleTheme.COLORS['text'],
            relief='solid',
            bd=1,
            borderwidth=1,
            highlightthickness=0,
            wrap=tk.WORD,
            state=tk.DISABLED,
            padx=SimpleTheme.SPACING['sm'],
            pady=SimpleTheme.SPACING['sm']
        )
        self.log_text.pack(fill=tk.BOTH, expand=True)
    
    def select_folder(self):
        """选择文件夹"""
        folder = filedialog.askdirectory(title="选择包含PDF文件的文件夹")
        if folder:
            self.current_folder = folder
            folder_name = Path(folder).name
            self.folder_label.configure(
                text=f"已选择：{folder_name}",
                fg=SimpleTheme.COLORS['text']
            )
            self.log_message(f"选择文件夹：{folder_name}")
            self.check_folder()
    
    def check_folder(self):
        """检查文件夹"""
        if not self.current_folder:
            return
        
        try:
            stats = self.pdf_extractor.get_pdf_statistics(self.current_folder)
            if stats['total_files'] > 0:
                self.log_message(f"发现 {stats['total_files']} 个PDF文件")
                self.status_label.configure(
                    text=f"就绪 - {stats['total_files']} 个文件",
                    fg=SimpleTheme.COLORS['success']
                )
                self.enable_buttons(True)
            else:
                self.log_message("未发现PDF文件")
                self.status_label.configure(
                    text="未发现PDF文件",
                    fg=SimpleTheme.COLORS['error']
                )
                self.enable_buttons(False)
        except Exception as e:
            self.log_message(f"检查失败：{str(e)}")
            self.status_label.configure(
                text="检查失败",
                fg=SimpleTheme.COLORS['error']
            )
            self.enable_buttons(False)
    
    def enable_buttons(self, enabled):
        """启用/禁用按钮"""
        state = tk.NORMAL if enabled else tk.DISABLED
        for btn in self.action_buttons:
            btn.configure(state=state)
    
    def log_message(self, message):
        """添加日志"""
        self.log_text.configure(state=tk.NORMAL)
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.log_text.configure(state=tk.DISABLED)
        self.root.update()
    
    def update_progress(self, status, progress=None):
        """更新进度"""
        self.status_label.configure(text=status)
        if progress is not None:
            self.progress['value'] = progress
        self.root.update()
    
    def start_processing(self, task_name, worker_func):
        """开始处理"""
        if self.processing:
            messagebox.showwarning("提示", "正在处理中，请稍候")
            return
        
        self.processing = True
        self.enable_buttons(False)
        self.log_message(f"开始{task_name}")
        
        thread = threading.Thread(target=worker_func, daemon=True)
        thread.start()
    
    def full_process(self):
        """完整流程"""
        if not self.current_folder:
            messagebox.showwarning("提示", "请先选择文件夹")
            return
        
        if messagebox.askyesno("确认", "执行完整流程？\n包括：提取→分析→重命名→归类"):
            self.start_processing("完整流程", self._full_process_worker)
    
    def rename_only(self):
        """仅重命名"""
        if not self.current_folder:
            messagebox.showwarning("提示", "请先选择文件夹")
            return
        
        if messagebox.askyesno("确认", "执行重命名操作？"):
            self.start_processing("重命名", self._rename_worker)
    
    def classify_only(self):
        """仅归类"""
        if not self.current_folder:
            messagebox.showwarning("提示", "请先选择文件夹")
            return
        
        if messagebox.askyesno("确认", "执行店铺归类？"):
            self.start_processing("店铺归类", self._classify_worker)
    
    def show_status(self):
        """显示状态"""
        if not self.current_folder:
            messagebox.showwarning("提示", "请先选择文件夹")
            return
        
        try:
            status = self.file_manager.get_file_status(self.current_folder)
            stats = self.pdf_extractor.get_pdf_statistics(self.current_folder)
            
            info = f"""文件状态：
            
总文件：{stats['total_files']} 个
原始文件：{status['original_count']} 个
已重命名：{status['renamed_count']} 个
店铺归类：{'已完成' if status['has_classification'] else '未完成'}"""
            
            messagebox.showinfo("文件状态", info)
        except Exception as e:
            messagebox.showerror("错误", f"获取状态失败：{str(e)}")
    
    def _full_process_worker(self):
        """完整流程工作线程"""
        try:
            self.message_queue.put(("progress", "提取PDF内容", 20))
            pdf_files = self.pdf_extractor.extract_pdf_content_parallel(self.current_folder)
            
            if not pdf_files:
                self.message_queue.put(("error", "未找到PDF文件"))
                return
            
            self.message_queue.put(("progress", "AI分析中", 50))
            ai_results = self.ai_analyzer.analyze_destinations_parallel(pdf_files)
            
            if not ai_results:
                self.message_queue.put(("error", "AI分析失败"))
                return
            
            self.message_queue.put(("progress", "重命名文件", 75))
            file_mappings = self.pdf_extractor.get_file_mappings(self.current_folder)
            success_count, errors = self.file_manager.execute_rename_with_lock(file_mappings, ai_results)
            
            self.message_queue.put(("progress", "店铺归类", 90))
            shop_data = self.file_manager.scan_renamed_files(self.current_folder)
            self.file_manager.create_shop_classification(self.current_folder, shop_data)
            
            self.message_queue.put(("success", f"完成！处理了 {success_count} 个文件"))
            
        except Exception as e:
            self.message_queue.put(("error", f"处理失败：{str(e)}"))
    
    def _rename_worker(self):
        """重命名工作线程"""
        try:
            self.message_queue.put(("progress", "提取PDF内容", 30))
            pdf_files = self.pdf_extractor.extract_pdf_content_parallel(self.current_folder)
            
            self.message_queue.put(("progress", "AI分析中", 70))
            ai_results = self.ai_analyzer.analyze_destinations_parallel(pdf_files)
            
            self.message_queue.put(("progress", "重命名文件", 90))
            file_mappings = self.pdf_extractor.get_file_mappings(self.current_folder)
            success_count, errors = self.file_manager.execute_rename_with_lock(file_mappings, ai_results)
            
            self.message_queue.put(("success", f"重命名完成！处理了 {success_count} 个文件"))
            
        except Exception as e:
            self.message_queue.put(("error", f"重命名失败：{str(e)}"))
    
    def _classify_worker(self):
        """归类工作线程"""
        try:
            self.message_queue.put(("progress", "扫描文件", 50))
            shop_data = self.file_manager.scan_renamed_files(self.current_folder)
            
            self.message_queue.put(("progress", "店铺归类", 90))
            success, errors = self.file_manager.create_shop_classification(self.current_folder, shop_data)
            
            self.message_queue.put(("success", f"归类完成！整理了 {len(shop_data)} 个店铺"))
            
        except Exception as e:
            self.message_queue.put(("error", f"归类失败：{str(e)}"))
    
    def check_queue(self):
        """检查消息队列"""
        try:
            while True:
                msg_type, *args = self.message_queue.get_nowait()
                
                if msg_type == "progress":
                    status, progress = args
                    self.update_progress(status, progress)
                    self.log_message(status)
                    
                elif msg_type == "success":
                    message = args[0]
                    self.processing = False
                    self.enable_buttons(True)
                    self.update_progress("完成", 100)
                    self.log_message(message)
                    messagebox.showinfo("完成", message)
                    self.check_folder()
                    
                elif msg_type == "error":
                    message = args[0]
                    self.processing = False
                    self.enable_buttons(True)
                    self.update_progress("错误", 0)
                    self.log_message(message)
                    messagebox.showerror("错误", message)
                    
        except queue.Empty:
            pass
        
        self.root.after(100, self.check_queue)
    
    def run(self):
        """运行应用"""
        self.log_message("PDF智能管理器已启动")
        self.log_message("请选择包含PDF文件的文件夹")
        self.root.mainloop()


def main():
    """主函数"""
    try:
        app = SimplePDFManagerGUI()
        app.run()
    except Exception as e:
        messagebox.showerror("错误", f"启动失败：{str(e)}")


if __name__ == "__main__":
    main()
