#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF智能管理器 - 简洁简约GUI版本
极简设计：白色背景、清晰层次、大量留白
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import queue
import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from config import config_manager
from pdf_extractor import PDFExtractor
from ai_analyzer import AIAnalyzer
from file_manager import FileManager


class SimpleTheme:
    """简约主题配置"""
    
    # 极简配色
    COLORS = {
        'bg': '#FFFFFF',           # 纯白背景
        'text': '#333333',         # 深灰文字
        'text_light': '#666666',   # 浅灰文字
        'text_muted': '#999999',   # 弱化文字
        'accent': '#007AFF',       # iOS蓝强调色
        'accent_hover': '#0056CC', # 强调色悬停
        'border': '#E5E5E5',       # 浅灰边框
        'success': '#34C759',      # 成功绿
        'error': '#FF3B30',        # 错误红
    }
    
    # 简洁字体
    FONTS = {
        'title': ('SF Pro Display', 24, 'normal'),      # 标题
        'heading': ('SF Pro Display', 16, 'normal'),    # 小标题
        'body': ('SF Pro Text', 13, 'normal'),          # 正文
        'caption': ('SF Pro Text', 11, 'normal'),       # 说明文字
        'button': ('SF Pro Text', 13, 'normal'),        # 按钮文字
    }
    
    # 间距系统
    SPACING = {
        'xs': 8, 'sm': 16, 'md': 24, 'lg': 32, 'xl': 48
    }


class SimpleButton(tk.Button):
    """简约按钮"""
    
    def __init__(self, parent, text="", command=None, variant='default', **kwargs):
        self.variant = variant
        
        # 根据变体设置颜色
        if variant == 'primary':
            bg = SimpleTheme.COLORS['accent']
            fg = '#FFFFFF'
            hover_bg = SimpleTheme.COLORS['accent_hover']
        else:
            bg = SimpleTheme.COLORS['bg']
            fg = SimpleTheme.COLORS['text']
            hover_bg = SimpleTheme.COLORS['border']
        
        super().__init__(
            parent,
            text=text,
            command=command,
            font=SimpleTheme.FONTS['button'],
            bg=bg,
            fg=fg,
            activebackground=hover_bg,
            activeforeground=fg,
            relief='flat',
            bd=0,
            cursor='hand2',
            padx=SimpleTheme.SPACING['md'],
            pady=SimpleTheme.SPACING['sm'],
            **kwargs
        )
        
        # 悬停效果
        self.bind('<Enter>', lambda e: self.configure(bg=hover_bg))
        self.bind('<Leave>', lambda e: self.configure(bg=bg))


class SimplePDFManagerGUI:
    """简约PDF管理器GUI"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.setup_ui()
        
        # 业务逻辑组件
        self.pdf_extractor = PDFExtractor()
        self.ai_analyzer = AIAnalyzer()
        self.file_manager = FileManager()
        
        # 状态变量
        self.current_folder = None
        self.processing = False
        
        # 消息队列
        self.message_queue = queue.Queue()
        self.check_queue()
    
    def setup_window(self):
        """设置窗口"""
        self.root.title("PDF智能管理器")
        self.root.geometry("800x600")
        self.root.minsize(700, 500)
        self.root.configure(bg=SimpleTheme.COLORS['bg'])
        
        # 居中显示
        self.center_window()
    
    def center_window(self):
        """窗口居中"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def setup_ui(self):
        """设置用户界面"""
        # 主容器 - 大量留白
        main_container = tk.Frame(
            self.root,
            bg=SimpleTheme.COLORS['bg'],
            padx=SimpleTheme.SPACING['xl'],
            pady=SimpleTheme.SPACING['xl']
        )
        main_container.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        self.setup_title(main_container)
        
        # 文件选择区域
        self.setup_file_selection(main_container)
        
        # 操作按钮区域
        self.setup_actions(main_container)
        
        # 状态显示区域
        self.setup_status(main_container)
        
        # 日志区域
        self.setup_log(main_container)
    
    def setup_title(self, parent):
        """设置标题"""
        title_frame = tk.Frame(parent, bg=SimpleTheme.COLORS['bg'])
        title_frame.pack(fill=tk.X, pady=(0, SimpleTheme.SPACING['lg']))

        # 左侧标题
        left_frame = tk.Frame(title_frame, bg=SimpleTheme.COLORS['bg'])
        left_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

        title_label = tk.Label(
            left_frame,
            text="PDF智能管理器",
            font=SimpleTheme.FONTS['title'],
            fg=SimpleTheme.COLORS['text'],
            bg=SimpleTheme.COLORS['bg']
        )
        title_label.pack(anchor='w')

        subtitle_label = tk.Label(
            left_frame,
            text="提取 · 分析 · 重命名 · 归类",
            font=SimpleTheme.FONTS['caption'],
            fg=SimpleTheme.COLORS['text_light'],
            bg=SimpleTheme.COLORS['bg']
        )
        subtitle_label.pack(anchor='w', pady=(SimpleTheme.SPACING['xs'], 0))

        # 右侧配置按钮
        config_btn = SimpleButton(
            title_frame,
            text="API配置",
            command=self.show_config
        )
        config_btn.pack(side=tk.RIGHT)
    
    def setup_file_selection(self, parent):
        """设置文件选择区域"""
        # 文件选择卡片
        file_card = tk.Frame(
            parent,
            bg=SimpleTheme.COLORS['bg'],
            relief='solid',
            bd=1,
            highlightbackground=SimpleTheme.COLORS['border'],
            highlightthickness=1
        )
        file_card.pack(fill=tk.X, pady=(0, SimpleTheme.SPACING['lg']))

        # 卡片内容
        card_content = tk.Frame(file_card, bg=SimpleTheme.COLORS['bg'])
        card_content.pack(fill=tk.X, padx=SimpleTheme.SPACING['lg'], pady=SimpleTheme.SPACING['lg'])

        # 标题
        title_label = tk.Label(
            card_content,
            text="1. 选择文件夹",
            font=SimpleTheme.FONTS['heading'],
            fg=SimpleTheme.COLORS['text'],
            bg=SimpleTheme.COLORS['bg']
        )
        title_label.pack(anchor='w', pady=(0, SimpleTheme.SPACING['sm']))

        # 按钮和状态容器
        selection_frame = tk.Frame(card_content, bg=SimpleTheme.COLORS['bg'])
        selection_frame.pack(fill=tk.X)

        # 选择按钮
        select_btn = SimpleButton(
            selection_frame,
            text="选择文件夹",
            command=self.select_folder,
            variant='primary'
        )
        select_btn.pack(side=tk.LEFT)

        # 文件统计
        self.file_stats_label = tk.Label(
            selection_frame,
            text="",
            font=SimpleTheme.FONTS['body'],
            fg=SimpleTheme.COLORS['text_light'],
            bg=SimpleTheme.COLORS['bg']
        )
        self.file_stats_label.pack(side=tk.RIGHT)

        # 当前文件夹显示
        self.folder_label = tk.Label(
            card_content,
            text="请选择包含PDF文件的文件夹",
            font=SimpleTheme.FONTS['body'],
            fg=SimpleTheme.COLORS['text_muted'],
            bg=SimpleTheme.COLORS['bg'],
            wraplength=600,
            justify=tk.LEFT
        )
        self.folder_label.pack(anchor='w', pady=(SimpleTheme.SPACING['sm'], 0))
    
    def setup_actions(self, parent):
        """设置操作区域"""
        # 操作卡片
        actions_card = tk.Frame(
            parent,
            bg=SimpleTheme.COLORS['bg'],
            relief='solid',
            bd=1,
            highlightbackground=SimpleTheme.COLORS['border'],
            highlightthickness=1
        )
        actions_card.pack(fill=tk.X, pady=(0, SimpleTheme.SPACING['lg']))

        # 卡片内容
        card_content = tk.Frame(actions_card, bg=SimpleTheme.COLORS['bg'])
        card_content.pack(fill=tk.X, padx=SimpleTheme.SPACING['lg'], pady=SimpleTheme.SPACING['lg'])

        # 标题
        title_label = tk.Label(
            card_content,
            text="2. 选择操作",
            font=SimpleTheme.FONTS['heading'],
            fg=SimpleTheme.COLORS['text'],
            bg=SimpleTheme.COLORS['bg']
        )
        title_label.pack(anchor='w', pady=(0, SimpleTheme.SPACING['sm']))

        # 按钮网格
        buttons_frame = tk.Frame(card_content, bg=SimpleTheme.COLORS['bg'])
        buttons_frame.pack(fill=tk.X)

        # 按钮配置
        buttons = [
            ("完整流程", "提取→分析→重命名→归类", self.full_process, 'primary'),
            ("仅重命名", "只执行AI分析和重命名", self.rename_only, 'default'),
            ("仅归类", "只执行店铺归类", self.classify_only, 'default'),
            ("查看状态", "查看当前文件状态", self.show_status, 'default')
        ]

        # 创建按钮
        self.action_buttons = []
        for i, (text, desc, command, variant) in enumerate(buttons):
            btn_frame = tk.Frame(buttons_frame, bg=SimpleTheme.COLORS['bg'])

            if i < 2:  # 前两个按钮在第一行
                btn_frame.grid(row=0, column=i, padx=(0, SimpleTheme.SPACING['sm']), pady=(0, SimpleTheme.SPACING['xs']), sticky='ew')
            else:  # 后两个按钮在第二行
                btn_frame.grid(row=1, column=i-2, padx=(0, SimpleTheme.SPACING['sm']), sticky='ew')

            btn = SimpleButton(
                btn_frame,
                text=text,
                command=command,
                variant=variant
            )
            btn.pack(fill=tk.X)

            # 按钮描述
            desc_label = tk.Label(
                btn_frame,
                text=desc,
                font=SimpleTheme.FONTS['caption'],
                fg=SimpleTheme.COLORS['text_muted'],
                bg=SimpleTheme.COLORS['bg']
            )
            desc_label.pack(pady=(SimpleTheme.SPACING['xs'], 0))

            self.action_buttons.append(btn)

        # 配置网格权重
        buttons_frame.columnconfigure(0, weight=1)
        buttons_frame.columnconfigure(1, weight=1)

        # 初始禁用按钮
        self.enable_buttons(False)
    
    def setup_status(self, parent):
        """设置状态区域"""
        # 分割线
        separator = tk.Frame(
            parent,
            height=1,
            bg=SimpleTheme.COLORS['border']
        )
        separator.pack(fill=tk.X, pady=(0, SimpleTheme.SPACING['lg']))
        
        # 状态容器
        status_frame = tk.Frame(parent, bg=SimpleTheme.COLORS['bg'])
        status_frame.pack(fill=tk.X, pady=(0, SimpleTheme.SPACING['lg']))
        
        # 状态文字
        self.status_label = tk.Label(
            status_frame,
            text="就绪",
            font=SimpleTheme.FONTS['body'],
            fg=SimpleTheme.COLORS['text'],
            bg=SimpleTheme.COLORS['bg']
        )
        self.status_label.pack(side=tk.LEFT)
        
        # 进度条
        self.progress = ttk.Progressbar(
            status_frame,
            mode='determinate',
            length=200
        )
        self.progress.pack(side=tk.RIGHT)
    
    def setup_log(self, parent):
        """设置日志区域"""
        # 分割线
        separator = tk.Frame(
            parent,
            height=1,
            bg=SimpleTheme.COLORS['border']
        )
        separator.pack(fill=tk.X, pady=(0, SimpleTheme.SPACING['lg']))
        
        # 日志标题
        log_title = tk.Label(
            parent,
            text="处理日志",
            font=SimpleTheme.FONTS['heading'],
            fg=SimpleTheme.COLORS['text'],
            bg=SimpleTheme.COLORS['bg']
        )
        log_title.pack(anchor='w', pady=(0, SimpleTheme.SPACING['sm']))
        
        # 日志文本框
        self.log_text = tk.Text(
            parent,
            height=8,
            font=('Monaco', 11),
            bg=SimpleTheme.COLORS['bg'],
            fg=SimpleTheme.COLORS['text'],
            relief='solid',
            bd=1,
            borderwidth=1,
            highlightthickness=0,
            wrap=tk.WORD,
            state=tk.DISABLED,
            padx=SimpleTheme.SPACING['sm'],
            pady=SimpleTheme.SPACING['sm']
        )
        self.log_text.pack(fill=tk.BOTH, expand=True)
    
    def select_folder(self):
        """选择文件夹"""
        folder = filedialog.askdirectory(title="选择包含PDF文件的文件夹")
        if folder:
            self.current_folder = folder
            folder_name = Path(folder).name
            self.folder_label.configure(
                text=f"已选择：{folder_name}",
                fg=SimpleTheme.COLORS['text']
            )
            self.log_message(f"选择文件夹：{folder_name}")
            self.check_folder()
    
    def check_folder(self):
        """检查文件夹"""
        if not self.current_folder:
            return

        try:
            stats = self.pdf_extractor.get_pdf_statistics(self.current_folder)

            if stats['total_files'] > 0:
                # 更新文件统计显示
                itinerary_count = stats.get('itinerary_files', 0)
                invoice_count = stats.get('invoice_files', 0)

                if itinerary_count > 0:
                    self.file_stats_label.configure(
                        text=f"行程单: {itinerary_count} | 发票: {invoice_count}",
                        fg=SimpleTheme.COLORS['success']
                    )
                    self.log_message(f"发现 {stats['total_files']} 个PDF文件 (行程单: {itinerary_count}, 发票: {invoice_count})")
                    self.status_label.configure(
                        text=f"就绪 - {stats['total_files']} 个文件",
                        fg=SimpleTheme.COLORS['success']
                    )
                    self.enable_buttons(True)
                else:
                    self.file_stats_label.configure(
                        text=f"总计: {stats['total_files']} 个文件",
                        fg=SimpleTheme.COLORS['text_light']
                    )
                    self.log_message(f"发现 {stats['total_files']} 个PDF文件，但没有行程单格式的文件")
                    self.status_label.configure(
                        text="未发现行程单文件",
                        fg=SimpleTheme.COLORS['error']
                    )
                    self.enable_buttons(False)
            else:
                self.file_stats_label.configure(text="", fg=SimpleTheme.COLORS['text_light'])
                self.log_message("未发现PDF文件")
                self.status_label.configure(
                    text="未发现PDF文件",
                    fg=SimpleTheme.COLORS['error']
                )
                self.enable_buttons(False)

        except Exception as e:
            self.file_stats_label.configure(text="", fg=SimpleTheme.COLORS['text_light'])
            self.log_message(f"检查失败：{str(e)}")
            self.status_label.configure(
                text="检查失败",
                fg=SimpleTheme.COLORS['error']
            )
            self.enable_buttons(False)
    
    def enable_buttons(self, enabled):
        """启用/禁用按钮"""
        state = tk.NORMAL if enabled else tk.DISABLED
        for btn in self.action_buttons:
            btn.configure(state=state)
    
    def log_message(self, message):
        """添加日志"""
        self.log_text.configure(state=tk.NORMAL)
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.log_text.configure(state=tk.DISABLED)
        self.root.update()
    
    def update_progress(self, status, progress=None):
        """更新进度"""
        self.status_label.configure(text=status)
        if progress is not None:
            self.progress['value'] = progress
        self.root.update()
    
    def start_processing(self, task_name, worker_func):
        """开始处理"""
        if self.processing:
            messagebox.showwarning("提示", "正在处理中，请稍候")
            return
        
        self.processing = True
        self.enable_buttons(False)
        self.log_message(f"开始{task_name}")
        
        thread = threading.Thread(target=worker_func, daemon=True)
        thread.start()
    
    def full_process(self):
        """完整流程"""
        if not self.current_folder:
            messagebox.showwarning("提示", "请先选择文件夹")
            return
        
        if messagebox.askyesno("确认", "执行完整流程？\n包括：提取→分析→重命名→归类"):
            self.start_processing("完整流程", self._full_process_worker)
    
    def rename_only(self):
        """仅重命名"""
        if not self.current_folder:
            messagebox.showwarning("提示", "请先选择文件夹")
            return
        
        if messagebox.askyesno("确认", "执行重命名操作？"):
            self.start_processing("重命名", self._rename_worker)
    
    def classify_only(self):
        """仅归类"""
        if not self.current_folder:
            messagebox.showwarning("提示", "请先选择文件夹")
            return
        
        if messagebox.askyesno("确认", "执行店铺归类？"):
            self.start_processing("店铺归类", self._classify_worker)
    
    def show_status(self):
        """显示状态"""
        if not self.current_folder:
            messagebox.showwarning("提示", "请先选择文件夹")
            return
        
        try:
            status = self.file_manager.get_file_status(self.current_folder)
            stats = self.pdf_extractor.get_pdf_statistics(self.current_folder)
            
            info = f"""文件状态：
            
总文件：{stats['total_files']} 个
原始文件：{status['original_count']} 个
已重命名：{status['renamed_count']} 个
店铺归类：{'已完成' if status['has_classification'] else '未完成'}"""
            
            messagebox.showinfo("文件状态", info)
        except Exception as e:
            messagebox.showerror("错误", f"获取状态失败：{str(e)}")

    def show_config(self):
        """显示API配置对话框"""
        config_window = tk.Toplevel(self.root)
        config_window.title("API配置")
        config_window.geometry("500x400")
        config_window.resizable(False, False)
        config_window.configure(bg=SimpleTheme.COLORS['bg'])

        # 居中显示
        config_window.transient(self.root)
        config_window.grab_set()

        x = self.root.winfo_x() + (self.root.winfo_width() // 2) - 250
        y = self.root.winfo_y() + (self.root.winfo_height() // 2) - 200
        config_window.geometry(f"500x400+{x}+{y}")

        # 主容器
        main_frame = tk.Frame(config_window, bg=SimpleTheme.COLORS['bg'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=SimpleTheme.SPACING['lg'], pady=SimpleTheme.SPACING['lg'])

        # 标题
        title_label = tk.Label(
            main_frame,
            text="API配置",
            font=SimpleTheme.FONTS['title'],
            fg=SimpleTheme.COLORS['text'],
            bg=SimpleTheme.COLORS['bg']
        )
        title_label.pack(pady=(0, SimpleTheme.SPACING['lg']))

        # API URL
        url_frame = tk.Frame(main_frame, bg=SimpleTheme.COLORS['bg'])
        url_frame.pack(fill=tk.X, pady=(0, SimpleTheme.SPACING['md']))

        tk.Label(
            url_frame,
            text="API地址:",
            font=SimpleTheme.FONTS['body'],
            fg=SimpleTheme.COLORS['text'],
            bg=SimpleTheme.COLORS['bg']
        ).pack(anchor='w')

        url_entry = tk.Entry(
            url_frame,
            font=SimpleTheme.FONTS['body'],
            relief='solid',
            bd=1
        )
        url_entry.pack(fill=tk.X, pady=(SimpleTheme.SPACING['xs'], 0))
        url_entry.insert(0, config_manager.api.url)

        # API Key
        key_frame = tk.Frame(main_frame, bg=SimpleTheme.COLORS['bg'])
        key_frame.pack(fill=tk.X, pady=(0, SimpleTheme.SPACING['md']))

        tk.Label(
            key_frame,
            text="API密钥:",
            font=SimpleTheme.FONTS['body'],
            fg=SimpleTheme.COLORS['text'],
            bg=SimpleTheme.COLORS['bg']
        ).pack(anchor='w')

        key_entry = tk.Entry(
            key_frame,
            font=SimpleTheme.FONTS['body'],
            relief='solid',
            bd=1,
            show='*'
        )
        key_entry.pack(fill=tk.X, pady=(SimpleTheme.SPACING['xs'], 0))
        key_entry.insert(0, config_manager.api.key)

        # 显示/隐藏密钥
        show_key_var = tk.BooleanVar()
        show_key_check = tk.Checkbutton(
            key_frame,
            text="显示密钥",
            variable=show_key_var,
            font=SimpleTheme.FONTS['caption'],
            fg=SimpleTheme.COLORS['text_light'],
            bg=SimpleTheme.COLORS['bg'],
            command=lambda: key_entry.configure(show='' if show_key_var.get() else '*')
        )
        show_key_check.pack(anchor='w', pady=(SimpleTheme.SPACING['xs'], 0))

        # 模型
        model_frame = tk.Frame(main_frame, bg=SimpleTheme.COLORS['bg'])
        model_frame.pack(fill=tk.X, pady=(0, SimpleTheme.SPACING['md']))

        tk.Label(
            model_frame,
            text="模型名称:",
            font=SimpleTheme.FONTS['body'],
            fg=SimpleTheme.COLORS['text'],
            bg=SimpleTheme.COLORS['bg']
        ).pack(anchor='w')

        model_entry = tk.Entry(
            model_frame,
            font=SimpleTheme.FONTS['body'],
            relief='solid',
            bd=1
        )
        model_entry.pack(fill=tk.X, pady=(SimpleTheme.SPACING['xs'], 0))
        model_entry.insert(0, config_manager.api.model)

        # 说明文字
        info_label = tk.Label(
            main_frame,
            text="提示：API密钥用于AI分析功能，如果没有密钥，只能使用基础功能。",
            font=SimpleTheme.FONTS['caption'],
            fg=SimpleTheme.COLORS['text_muted'],
            bg=SimpleTheme.COLORS['bg'],
            wraplength=450,
            justify=tk.LEFT
        )
        info_label.pack(pady=(SimpleTheme.SPACING['md'], 0))

        # 按钮区域
        button_frame = tk.Frame(main_frame, bg=SimpleTheme.COLORS['bg'])
        button_frame.pack(fill=tk.X, pady=(SimpleTheme.SPACING['lg'], 0))

        # 测试连接按钮
        test_btn = SimpleButton(
            button_frame,
            text="测试连接",
            command=lambda: self.test_api_connection(url_entry.get(), key_entry.get(), model_entry.get())
        )
        test_btn.pack(side=tk.LEFT)

        # 保存按钮
        save_btn = SimpleButton(
            button_frame,
            text="保存",
            variant='primary',
            command=lambda: self.save_config(config_window, url_entry.get(), key_entry.get(), model_entry.get())
        )
        save_btn.pack(side=tk.RIGHT, padx=(SimpleTheme.SPACING['sm'], 0))

        # 取消按钮
        cancel_btn = SimpleButton(
            button_frame,
            text="取消",
            command=config_window.destroy
        )
        cancel_btn.pack(side=tk.RIGHT)

    def test_api_connection(self, url, key, model):
        """测试API连接"""
        if not url or not key:
            messagebox.showwarning("提示", "请填写API地址和密钥")
            return

        try:
            import requests
            headers = {
                'Authorization': f'Bearer {key}',
                'Content-Type': 'application/json'
            }
            data = {
                'model': model,
                'messages': [{'role': 'user', 'content': '测试连接'}],
                'max_tokens': 10
            }

            response = requests.post(url, headers=headers, json=data, timeout=10)
            if response.status_code == 200:
                messagebox.showinfo("测试成功", "API连接正常！")
            else:
                messagebox.showerror("测试失败", f"连接失败：{response.status_code}")
        except Exception as e:
            messagebox.showerror("测试失败", f"连接错误：{str(e)}")

    def save_config(self, window, url, key, model):
        """保存配置"""
        try:
            config_manager.update_api_config(
                url=url,
                key=key,
                model=model
            )
            if config_manager.save_config():
                messagebox.showinfo("保存成功", "配置已保存")
                window.destroy()
            else:
                messagebox.showerror("保存失败", "配置保存失败")
        except Exception as e:
            messagebox.showerror("保存失败", f"保存错误：{str(e)}")
    
    def _full_process_worker(self):
        """完整流程工作线程"""
        try:
            self.message_queue.put(("progress", "提取PDF内容", 20))
            pdf_files = self.pdf_extractor.extract_pdf_content_parallel(self.current_folder)
            
            if not pdf_files:
                self.message_queue.put(("error", "未找到PDF文件"))
                return
            
            self.message_queue.put(("progress", "AI分析中", 50))
            ai_results = self.ai_analyzer.analyze_destinations_parallel(pdf_files)
            
            if not ai_results:
                self.message_queue.put(("error", "AI分析失败"))
                return
            
            self.message_queue.put(("progress", "重命名文件", 75))
            file_mappings = self.pdf_extractor.get_file_mappings(self.current_folder)
            success_count, errors = self.file_manager.execute_rename_with_lock(file_mappings, ai_results)
            
            self.message_queue.put(("progress", "店铺归类", 90))
            shop_data = self.file_manager.scan_renamed_files(self.current_folder)
            self.file_manager.create_shop_classification(self.current_folder, shop_data)
            
            self.message_queue.put(("success", f"完成！处理了 {success_count} 个文件"))
            
        except Exception as e:
            self.message_queue.put(("error", f"处理失败：{str(e)}"))
    
    def _rename_worker(self):
        """重命名工作线程"""
        try:
            self.message_queue.put(("progress", "提取PDF内容", 30))
            pdf_files = self.pdf_extractor.extract_pdf_content_parallel(self.current_folder)
            
            self.message_queue.put(("progress", "AI分析中", 70))
            ai_results = self.ai_analyzer.analyze_destinations_parallel(pdf_files)
            
            self.message_queue.put(("progress", "重命名文件", 90))
            file_mappings = self.pdf_extractor.get_file_mappings(self.current_folder)
            success_count, errors = self.file_manager.execute_rename_with_lock(file_mappings, ai_results)
            
            self.message_queue.put(("success", f"重命名完成！处理了 {success_count} 个文件"))
            
        except Exception as e:
            self.message_queue.put(("error", f"重命名失败：{str(e)}"))
    
    def _classify_worker(self):
        """归类工作线程"""
        try:
            self.message_queue.put(("progress", "扫描文件", 50))
            shop_data = self.file_manager.scan_renamed_files(self.current_folder)
            
            self.message_queue.put(("progress", "店铺归类", 90))
            success, errors = self.file_manager.create_shop_classification(self.current_folder, shop_data)
            
            self.message_queue.put(("success", f"归类完成！整理了 {len(shop_data)} 个店铺"))
            
        except Exception as e:
            self.message_queue.put(("error", f"归类失败：{str(e)}"))
    
    def check_queue(self):
        """检查消息队列"""
        try:
            while True:
                msg_type, *args = self.message_queue.get_nowait()
                
                if msg_type == "progress":
                    status, progress = args
                    self.update_progress(status, progress)
                    self.log_message(status)
                    
                elif msg_type == "success":
                    message = args[0]
                    self.processing = False
                    self.enable_buttons(True)
                    self.update_progress("完成", 100)
                    self.log_message(message)
                    messagebox.showinfo("完成", message)
                    self.check_folder()
                    
                elif msg_type == "error":
                    message = args[0]
                    self.processing = False
                    self.enable_buttons(True)
                    self.update_progress("错误", 0)
                    self.log_message(message)
                    messagebox.showerror("错误", message)
                    
        except queue.Empty:
            pass
        
        self.root.after(100, self.check_queue)
    
    def run(self):
        """运行应用"""
        self.log_message("PDF智能管理器已启动")
        self.log_message("请选择包含PDF文件的文件夹")
        self.root.mainloop()


def main():
    """主函数"""
    try:
        app = SimplePDFManagerGUI()
        app.run()
    except Exception as e:
        messagebox.showerror("错误", f"启动失败：{str(e)}")


if __name__ == "__main__":
    main()
