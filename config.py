#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
统一管理API配置、文件路径、提示词等
"""

import os
import json
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path


@dataclass
class APIConfig:
    """API配置类"""
    url: str
    key: str
    model: str
    timeout: int = 60
    max_retries: int = 3
    retry_delay: float = 1.0


@dataclass
class ProcessingConfig:
    """处理配置类"""
    max_workers: int = 4
    batch_size: int = 10
    enable_parallel: bool = True
    backup_enabled: bool = True


@dataclass
class UIConfig:
    """界面配置类"""
    language: str = "zh"
    theme: str = "default"
    show_progress: bool = True


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = Path(config_file)
        self.logger = logging.getLogger(__name__)
        
        # 默认配置
        self.default_config = {
            "api": {
                "url": "https://api.siliconflow.cn/v1/chat/completions",
                "key": "",
                "model": "deepseek-ai/DeepSeek-V3",
                "timeout": 60,
                "max_retries": 3,
                "retry_delay": 1.0
            },
            "processing": {
                "max_workers": 4,
                "batch_size": 10,
                "enable_parallel": True,
                "backup_enabled": True
            },
            "ui": {
                "language": "zh",
                "theme": "default",
                "show_progress": True
            }
        }
        
        self._config = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # 合并默认配置，确保所有必需的键都存在
                    return self._merge_config(self.default_config, config)
            else:
                self.logger.info("配置文件不存在，使用默认配置")
                return self.default_config.copy()
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            return self.default_config.copy()
    
    def save_config(self) -> bool:
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, indent=2, ensure_ascii=False)
            self.logger.info("配置文件保存成功")
            return True
        except Exception as e:
            self.logger.error(f"保存配置文件失败: {e}")
            return False
    
    def _merge_config(self, default: Dict, user: Dict) -> Dict:
        """合并配置，用户配置覆盖默认配置"""
        result = default.copy()
        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_config(result[key], value)
            else:
                result[key] = value
        return result
    
    @property
    def api(self) -> APIConfig:
        """获取API配置"""
        api_config = self._config.get("api", {})
        return APIConfig(**api_config)
    
    @property
    def processing(self) -> ProcessingConfig:
        """获取处理配置"""
        proc_config = self._config.get("processing", {})
        return ProcessingConfig(**proc_config)
    
    @property
    def ui(self) -> UIConfig:
        """获取UI配置"""
        ui_config = self._config.get("ui", {})
        return UIConfig(**ui_config)
    
    def update_api_config(self, **kwargs) -> None:
        """更新API配置"""
        if "api" not in self._config:
            self._config["api"] = {}
        self._config["api"].update(kwargs)
    
    def update_processing_config(self, **kwargs) -> None:
        """更新处理配置"""
        if "processing" not in self._config:
            self._config["processing"] = {}
        self._config["processing"].update(kwargs)
    
    def get_api_key_from_env(self) -> Optional[str]:
        """从环境变量获取API密钥"""
        return os.getenv("PDF_MANAGER_API_KEY")
    
    def validate_config(self) -> bool:
        """验证配置有效性"""
        try:
            api_config = self.api
            if not api_config.url or not api_config.key:
                self.logger.error("API配置不完整")
                return False
            
            proc_config = self.processing
            if proc_config.max_workers <= 0:
                self.logger.error("max_workers必须大于0")
                return False
            
            return True
        except Exception as e:
            self.logger.error(f"配置验证失败: {e}")
            return False


class PromptManager:
    """提示词管理器"""
    
    def __init__(self, prompts_file: str = "prompts.json"):
        self.prompts_file = Path(prompts_file)
        self.logger = logging.getLogger(__name__)
        self.prompts = self.load_prompts()
    
    def load_prompts(self) -> Dict[str, str]:
        """加载提示词"""
        default_prompts = {
            "destination_extraction": """# Role: 行程终点提取器

## Profile
- language: 中文
- description: 专业分析用户提供的行程单内容，高效识别并原样输出最后一个行程的终点信息。
- personality: 严谨、保守、高效；拒绝任何改动或添加，坚持一丝不苟的输出方式。

## Rules
1. 基本原则：
   - 忠实原样输出：必须一字不差地复制终点信息，包括所有符号、地址、店铺信息或括号内容。
   - 最大序号选择：仅以行程单中序号最大的记录（即最后一个行程）为准。
   - 纯信息返回：输出只包含终点信息本身，拒绝附带解释、原因或上下文。

2. 行为准则：
   - 输入解析：解析内容，提取所有行程记录列表。
   - 终点定位：识别并选择列表中最高序号（最后一个）的记录。
   - 直接复制：原样复制终点信息部分，不进行任何处理。

3. 限制条件：
   - 禁止修改：终点信息不允许任何编辑、简化或优化。
   - 禁用解释：不输出额外文本，如提示、错误消息或过程描述。

## 任务
从以下行程单中精确提取最后一个行程的完整终点信息：

{content}

请直接返回完整的终点信息："""
        }
        
        try:
            if self.prompts_file.exists():
                with open(self.prompts_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                self.save_prompts(default_prompts)
                return default_prompts
        except Exception as e:
            self.logger.error(f"加载提示词失败: {e}")
            return default_prompts
    
    def save_prompts(self, prompts: Dict[str, str]) -> bool:
        """保存提示词"""
        try:
            with open(self.prompts_file, 'w', encoding='utf-8') as f:
                json.dump(prompts, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            self.logger.error(f"保存提示词失败: {e}")
            return False
    
    def get_prompt(self, key: str) -> str:
        """获取指定提示词"""
        return self.prompts.get(key, "")
    
    def update_prompt(self, key: str, prompt: str) -> None:
        """更新提示词"""
        self.prompts[key] = prompt


# 全局配置实例
config_manager = ConfigManager()
prompt_manager = PromptManager()
