#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF智能管理器 - 现代Web风格GUI启动脚本
"""

import sys
import tkinter as tk
from tkinter import messagebox
from pathlib import Path

def main():
    """主函数"""
    print("🚀 启动PDF智能管理器 - 现代Web风格GUI...")
    
    try:
        # 检查依赖
        try:
            import requests
            import PyPDF2
        except ImportError as e:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror(
                "依赖缺失", 
                f"❌ 缺少依赖：{str(e)}\n\n"
                "请运行：pip install requests PyPDF2"
            )
            return
        
        # 启动现代Web风格GUI
        from modern_web_gui import ModernWebPDFManagerGUI
        
        print("✅ 依赖检查通过")
        print("🎨 加载现代Web风格界面...")
        
        app = ModernWebPDFManagerGUI()
        app.run()
        
    except Exception as e:
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("启动失败", f"❌ 启动失败：{str(e)}")

if __name__ == "__main__":
    main()
