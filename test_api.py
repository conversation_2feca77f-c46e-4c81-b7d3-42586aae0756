#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API连接测试脚本
"""

import requests
import json

# API配置
API_URL = "https://cloud.siliconflow.cn/v1/chat/completions"
API_KEY = "sk-vfehejijukxqzoumteewnyklkelivcpyczrmwccctrstxmue"
MODEL = "deepseek-ai/DeepSeek-V3"

def test_api():
    """测试API连接"""
    headers = {
        'Authorization': f'Bearer {API_KEY}',
        'Content-Type': 'application/json'
    }
    
    data = {
        'model': MODEL,
        'messages': [{'role': 'user', 'content': '你好，请回复"API连接成功"'}],
        'temperature': 0.1,
        'max_tokens': 50
    }
    
    try:
        print(f"测试API: {API_URL}")
        print(f"使用模型: {MODEL}")
        print("发送请求...")
        
        response = requests.post(API_URL, headers=headers, json=data, timeout=30)
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            content = result['choices'][0]['message']['content']
            print(f"API响应成功: {content}")
            return True
        else:
            print(f"API请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"API测试失败: {e}")
        return False

if __name__ == "__main__":
    test_api()
