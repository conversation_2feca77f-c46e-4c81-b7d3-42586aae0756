#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF智能管理器 - 现代化GUI版本
采用最新设计趋势：深色模式、卡片布局、动画效果
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import queue
import sys
from pathlib import Path
from typing import Optional, Dict, Any
import json
import time

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from config import config_manager
from pdf_extractor import PDFExtractor
from ai_analyzer import AIAnalyzer
from file_manager import FileManager


class ModernTheme:
    """现代化主题配置"""
    
    # 深色主题
    DARK_THEME = {
        'bg_primary': '#0F172A',      # 主背景 - 深蓝灰
        'bg_secondary': '#1E293B',    # 次要背景
        'bg_tertiary': '#334155',     # 第三背景
        'surface': '#475569',         # 表面色
        'primary': '#3B82F6',         # 主色调 - 蓝色
        'primary_hover': '#2563EB',   # 主色调悬停
        'success': '#10B981',         # 成功色 - 绿色
        'warning': '#F59E0B',         # 警告色 - 橙色
        'error': '#EF4444',           # 错误色 - 红色
        'text_primary': '#F8FAFC',    # 主文字色
        'text_secondary': '#CBD5E1',  # 次要文字色
        'text_muted': '#94A3B8',      # 弱化文字色
        'border': '#475569',          # 边框色
        'shadow': '#000000',          # 阴影色
    }
    
    # 浅色主题
    LIGHT_THEME = {
        'bg_primary': '#FFFFFF',      # 主背景 - 白色
        'bg_secondary': '#F8FAFC',    # 次要背景
        'bg_tertiary': '#F1F5F9',     # 第三背景
        'surface': '#FFFFFF',         # 表面色
        'primary': '#3B82F6',         # 主色调 - 蓝色
        'primary_hover': '#2563EB',   # 主色调悬停
        'success': '#10B981',         # 成功色 - 绿色
        'warning': '#F59E0B',         # 警告色 - 橙色
        'error': '#EF4444',           # 错误色 - 红色
        'text_primary': '#0F172A',    # 主文字色
        'text_secondary': '#475569',  # 次要文字色
        'text_muted': '#64748B',      # 弱化文字色
        'border': '#E2E8F0',          # 边框色
        'shadow': '#64748B',          # 阴影色
    }
    
    # 字体系统
    FONTS = {
        'title': ('Segoe UI', 20, 'bold'),
        'heading': ('Segoe UI', 14, 'bold'),
        'subheading': ('Segoe UI', 12, 'bold'),
        'body': ('Segoe UI', 10),
        'caption': ('Segoe UI', 9),
        'button': ('Segoe UI', 10, 'bold'),
        'mono': ('Consolas', 9),
    }
    
    # 间距系统
    SPACING = {
        'xs': 4, 'sm': 8, 'md': 16, 'lg': 24, 'xl': 32, 'xxl': 48
    }
    
    def __init__(self, dark_mode=True):
        self.dark_mode = dark_mode
        self.colors = self.DARK_THEME if dark_mode else self.LIGHT_THEME
    
    def toggle_theme(self):
        """切换主题"""
        self.dark_mode = not self.dark_mode
        self.colors = self.DARK_THEME if self.dark_mode else self.LIGHT_THEME


class ModernCard(tk.Frame):
    """现代化卡片组件"""
    
    def __init__(self, parent, theme: ModernTheme, title="", **kwargs):
        super().__init__(parent, **kwargs)
        self.theme = theme
        self.title = title
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        # 配置卡片样式
        self.configure(
            bg=self.theme.colors['surface'],
            relief='flat',
            bd=0,
            padx=self.theme.SPACING['md'],
            pady=self.theme.SPACING['md']
        )
        
        # 标题
        if self.title:
            title_label = tk.Label(
                self,
                text=self.title,
                font=self.theme.FONTS['subheading'],
                fg=self.theme.colors['text_primary'],
                bg=self.theme.colors['surface'],
                anchor='w'
            )
            title_label.pack(fill=tk.X, pady=(0, self.theme.SPACING['sm']))


class ModernButton(tk.Button):
    """现代化按钮组件"""
    
    def __init__(self, parent, theme: ModernTheme, variant='primary', **kwargs):
        self.theme = theme
        self.variant = variant
        self.original_bg = self._get_bg_color()
        self.hover_bg = self._get_hover_color()
        
        super().__init__(
            parent,
            font=self.theme.FONTS['button'],
            bg=self.original_bg,
            fg=self._get_text_color(),
            activebackground=self.hover_bg,
            activeforeground=self._get_text_color(),
            relief='flat',
            bd=0,
            cursor='hand2',
            padx=self.theme.SPACING['lg'],
            pady=self.theme.SPACING['sm'],
            **kwargs
        )
        
        # 绑定悬停效果
        self.bind('<Enter>', self._on_enter)
        self.bind('<Leave>', self._on_leave)
    
    def _get_bg_color(self):
        """获取背景色"""
        color_map = {
            'primary': self.theme.colors['primary'],
            'success': self.theme.colors['success'],
            'warning': self.theme.colors['warning'],
            'error': self.theme.colors['error'],
            'secondary': self.theme.colors['bg_tertiary']
        }
        return color_map.get(self.variant, self.theme.colors['primary'])
    
    def _get_hover_color(self):
        """获取悬停色"""
        if self.variant == 'primary':
            return self.theme.colors['primary_hover']
        return self.original_bg
    
    def _get_text_color(self):
        """获取文字色"""
        if self.variant == 'secondary':
            return self.theme.colors['text_primary']
        return '#FFFFFF'
    
    def _on_enter(self, event):
        """鼠标进入"""
        self.configure(bg=self.hover_bg)
    
    def _on_leave(self, event):
        """鼠标离开"""
        self.configure(bg=self.original_bg)


class ModernProgressBar(tk.Frame):
    """现代化进度条"""
    
    def __init__(self, parent, theme: ModernTheme, **kwargs):
        super().__init__(parent, **kwargs)
        self.theme = theme
        self.progress_value = 0
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        self.configure(
            bg=self.theme.colors['bg_tertiary'],
            height=8,
            relief='flat'
        )
        
        # 进度条
        self.progress_bar = tk.Frame(
            self,
            bg=self.theme.colors['primary'],
            height=8
        )
        self.progress_bar.place(x=0, y=0, width=0, height=8)
    
    def set_progress(self, value: float):
        """设置进度值 (0-100)"""
        self.progress_value = max(0, min(100, value))
        width = int(self.winfo_width() * self.progress_value / 100)
        self.progress_bar.place(width=width)
        self.update()


class ModernStatCard(ModernCard):
    """现代化统计卡片"""
    
    def __init__(self, parent, theme: ModernTheme, title="", value="0", 
                 icon="📊", color="primary", **kwargs):
        self.value_text = value
        self.icon = icon
        self.color = color
        super().__init__(parent, theme, title, **kwargs)
        self.setup_content()
    
    def setup_content(self):
        """设置内容"""
        # 图标和数值容器
        content_frame = tk.Frame(self, bg=self.theme.colors['surface'])
        content_frame.pack(fill=tk.X)
        
        # 图标
        icon_label = tk.Label(
            content_frame,
            text=self.icon,
            font=('Segoe UI Emoji', 24),
            bg=self.theme.colors['surface'],
            fg=self.theme.colors[self.color]
        )
        icon_label.pack(side=tk.LEFT, padx=(0, self.theme.SPACING['md']))
        
        # 数值和标题容器
        text_frame = tk.Frame(content_frame, bg=self.theme.colors['surface'])
        text_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # 数值
        self.value_label = tk.Label(
            text_frame,
            text=self.value_text,
            font=self.theme.FONTS['title'],
            fg=self.theme.colors['text_primary'],
            bg=self.theme.colors['surface'],
            anchor='w'
        )
        self.value_label.pack(fill=tk.X)
        
        # 标题
        if self.title:
            title_label = tk.Label(
                text_frame,
                text=self.title,
                font=self.theme.FONTS['caption'],
                fg=self.theme.colors['text_muted'],
                bg=self.theme.colors['surface'],
                anchor='w'
            )
            title_label.pack(fill=tk.X)
    
    def update_value(self, value: str):
        """更新数值"""
        self.value_text = value
        self.value_label.configure(text=value)


class ModernDropZone(ModernCard):
    """现代化拖拽区域"""
    
    def __init__(self, parent, theme: ModernTheme, callback=None, **kwargs):
        self.callback = callback
        self.is_dragging = False
        super().__init__(parent, theme, **kwargs)
        self.setup_drop_zone()
    
    def setup_drop_zone(self):
        """设置拖拽区域"""
        # 主容器
        self.configure(
            bg=self.theme.colors['bg_tertiary'],
            relief='dashed',
            bd=2,
            highlightbackground=self.theme.colors['border'],
            highlightthickness=2
        )
        
        # 内容区域
        content_frame = tk.Frame(self, bg=self.theme.colors['bg_tertiary'])
        content_frame.pack(expand=True, fill=tk.BOTH, padx=self.theme.SPACING['xl'], 
                          pady=self.theme.SPACING['xl'])
        
        # 图标
        icon_label = tk.Label(
            content_frame,
            text="📁",
            font=('Segoe UI Emoji', 48),
            bg=self.theme.colors['bg_tertiary'],
            fg=self.theme.colors['text_muted']
        )
        icon_label.pack(pady=(0, self.theme.SPACING['md']))
        
        # 主文字
        main_label = tk.Label(
            content_frame,
            text="拖拽文件夹到此处",
            font=self.theme.FONTS['heading'],
            fg=self.theme.colors['text_primary'],
            bg=self.theme.colors['bg_tertiary']
        )
        main_label.pack(pady=(0, self.theme.SPACING['sm']))
        
        # 副文字
        sub_label = tk.Label(
            content_frame,
            text="或点击下方按钮选择文件夹",
            font=self.theme.FONTS['body'],
            fg=self.theme.colors['text_muted'],
            bg=self.theme.colors['bg_tertiary']
        )
        sub_label.pack(pady=(0, self.theme.SPACING['lg']))
        
        # 选择按钮
        select_btn = ModernButton(
            content_frame,
            self.theme,
            text="📂 选择文件夹",
            variant='primary',
            command=self.select_folder
        )
        select_btn.pack()
        
        # 绑定拖拽事件（简化版，实际需要更复杂的实现）
        self.bind('<Button-1>', self.on_click)
    
    def select_folder(self):
        """选择文件夹"""
        folder = filedialog.askdirectory(title="选择包含PDF文件的文件夹")
        if folder and self.callback:
            self.callback(folder)
    
    def on_click(self, event):
        """点击事件"""
        self.select_folder()


class ModernPDFManagerGUI:
    """现代化PDF管理器GUI"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.theme = ModernTheme(dark_mode=True)  # 默认深色模式
        self.setup_window()
        self.setup_ui()
        
        # 业务逻辑组件
        self.pdf_extractor = PDFExtractor()
        self.ai_analyzer = AIAnalyzer()
        self.file_manager = FileManager()
        
        # 状态变量
        self.current_folder = None
        self.processing = False
        
        # 消息队列
        self.message_queue = queue.Queue()
        self.check_queue()
    
    def setup_window(self):
        """设置主窗口"""
        self.root.title("PDF智能管理器 v2.0")
        self.root.geometry("1400x900")
        self.root.minsize(1000, 700)
        self.root.configure(bg=self.theme.colors['bg_primary'])
        
        # 居中显示
        self.center_window()
    
    def center_window(self):
        """窗口居中"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def setup_ui(self):
        """设置用户界面"""
        # 主容器
        main_container = tk.Frame(
            self.root, 
            bg=self.theme.colors['bg_primary'],
            padx=self.theme.SPACING['xl'],
            pady=self.theme.SPACING['xl']
        )
        main_container.pack(fill=tk.BOTH, expand=True)
        
        # 顶部栏
        self.setup_header(main_container)
        
        # 主内容区
        self.setup_main_content(main_container)
    
    def setup_header(self, parent):
        """设置顶部栏"""
        header_frame = tk.Frame(parent, bg=self.theme.colors['bg_primary'])
        header_frame.pack(fill=tk.X, pady=(0, self.theme.SPACING['xl']))
        
        # 左侧：标题和状态
        left_frame = tk.Frame(header_frame, bg=self.theme.colors['bg_primary'])
        left_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # 标题
        title_label = tk.Label(
            left_frame,
            text="🚀 PDF智能管理器",
            font=self.theme.FONTS['title'],
            fg=self.theme.colors['text_primary'],
            bg=self.theme.colors['bg_primary']
        )
        title_label.pack(side=tk.LEFT)
        
        # 版本标签
        version_label = tk.Label(
            left_frame,
            text="v2.0",
            font=self.theme.FONTS['caption'],
            fg=self.theme.colors['text_muted'],
            bg=self.theme.colors['bg_primary']
        )
        version_label.pack(side=tk.LEFT, padx=(self.theme.SPACING['sm'], 0))
        
        # 右侧：控制按钮
        right_frame = tk.Frame(header_frame, bg=self.theme.colors['bg_primary'])
        right_frame.pack(side=tk.RIGHT)
        
        # 主题切换按钮
        theme_btn = ModernButton(
            right_frame,
            self.theme,
            text="🌙" if self.theme.dark_mode else "☀️",
            variant='secondary',
            command=self.toggle_theme
        )
        theme_btn.pack(side=tk.RIGHT, padx=(self.theme.SPACING['sm'], 0))
        
        # 设置按钮
        settings_btn = ModernButton(
            right_frame,
            self.theme,
            text="⚙️ 设置",
            variant='secondary',
            command=self.show_settings
        )
        settings_btn.pack(side=tk.RIGHT)
    
    def setup_main_content(self, parent):
        """设置主内容区"""
        # 主内容容器
        content_frame = tk.Frame(parent, bg=self.theme.colors['bg_primary'])
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # 左侧面板
        self.setup_left_panel(content_frame)
        
        # 右侧面板
        self.setup_right_panel(content_frame)

    def setup_left_panel(self, parent):
        """设置左侧面板"""
        left_panel = tk.Frame(parent, bg=self.theme.colors['bg_primary'])
        left_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True,
                       padx=(0, self.theme.SPACING['lg']))

        # 文件选择区域
        self.setup_file_selection_area(left_panel)

        # 操作按钮区域
        self.setup_action_area(left_panel)

        # 日志区域
        self.setup_log_area(left_panel)

    def setup_right_panel(self, parent):
        """设置右侧面板"""
        right_panel = tk.Frame(parent, bg=self.theme.colors['bg_primary'], width=350)
        right_panel.pack(side=tk.RIGHT, fill=tk.Y)
        right_panel.pack_propagate(False)

        # 统计卡片区域
        self.setup_stats_area(right_panel)

        # 进度区域
        self.setup_progress_area(right_panel)

        # 快速设置区域
        self.setup_quick_settings(right_panel)

    def setup_file_selection_area(self, parent):
        """设置文件选择区域"""
        # 区域标题
        title_frame = tk.Frame(parent, bg=self.theme.colors['bg_primary'])
        title_frame.pack(fill=tk.X, pady=(0, self.theme.SPACING['md']))

        title_label = tk.Label(
            title_frame,
            text="📁 文件夹选择",
            font=self.theme.FONTS['heading'],
            fg=self.theme.colors['text_primary'],
            bg=self.theme.colors['bg_primary']
        )
        title_label.pack(side=tk.LEFT)

        # 拖拽区域
        self.drop_zone = ModernDropZone(
            parent,
            self.theme,
            callback=self.on_folder_selected
        )
        self.drop_zone.pack(fill=tk.X, pady=(0, self.theme.SPACING['lg']))

        # 当前文件夹显示
        self.folder_info_frame = ModernCard(parent, self.theme, "当前文件夹")
        self.folder_info_frame.pack(fill=tk.X, pady=(0, self.theme.SPACING['lg']))

        self.folder_path_label = tk.Label(
            self.folder_info_frame,
            text="未选择文件夹",
            font=self.theme.FONTS['body'],
            fg=self.theme.colors['text_muted'],
            bg=self.theme.colors['surface'],
            wraplength=400,
            justify=tk.LEFT
        )
        self.folder_path_label.pack(fill=tk.X)

    def setup_action_area(self, parent):
        """设置操作区域"""
        # 区域标题
        title_label = tk.Label(
            parent,
            text="🚀 处理操作",
            font=self.theme.FONTS['heading'],
            fg=self.theme.colors['text_primary'],
            bg=self.theme.colors['bg_primary']
        )
        title_label.pack(anchor=tk.W, pady=(0, self.theme.SPACING['md']))

        # 操作卡片
        action_card = ModernCard(parent, self.theme)
        action_card.pack(fill=tk.X, pady=(0, self.theme.SPACING['lg']))

        # 操作按钮网格
        buttons_frame = tk.Frame(action_card, bg=self.theme.colors['surface'])
        buttons_frame.pack(fill=tk.X, padx=self.theme.SPACING['sm'])

        # 按钮配置
        self.action_buttons = []
        buttons_config = [
            ("🔄 完整流程", "一键完成所有处理步骤", self.full_process, 'primary'),
            ("📝 仅重命名", "只执行文件重命名操作", self.rename_only, 'success'),
            ("📂 仅归类", "只执行店铺归类操作", self.classify_only, 'warning'),
            ("📊 查看状态", "查看当前文件处理状态", self.show_status, 'secondary'),
        ]

        for i, (text, desc, command, variant) in enumerate(buttons_config):
            btn_frame = tk.Frame(buttons_frame, bg=self.theme.colors['surface'])
            btn_frame.pack(fill=tk.X, pady=self.theme.SPACING['sm'])

            btn = ModernButton(
                btn_frame,
                self.theme,
                text=text,
                variant=variant,
                command=command
            )
            btn.pack(side=tk.LEFT)

            desc_label = tk.Label(
                btn_frame,
                text=desc,
                font=self.theme.FONTS['caption'],
                fg=self.theme.colors['text_muted'],
                bg=self.theme.colors['surface']
            )
            desc_label.pack(side=tk.LEFT, padx=(self.theme.SPACING['md'], 0))

            self.action_buttons.append(btn)

    def setup_log_area(self, parent):
        """设置日志区域"""
        # 区域标题
        title_label = tk.Label(
            parent,
            text="📋 处理日志",
            font=self.theme.FONTS['heading'],
            fg=self.theme.colors['text_primary'],
            bg=self.theme.colors['bg_primary']
        )
        title_label.pack(anchor=tk.W, pady=(0, self.theme.SPACING['md']))

        # 日志卡片
        log_card = ModernCard(parent, self.theme)
        log_card.pack(fill=tk.BOTH, expand=True)

        # 日志文本区域
        log_frame = tk.Frame(log_card, bg=self.theme.colors['surface'])
        log_frame.pack(fill=tk.BOTH, expand=True, padx=self.theme.SPACING['sm'])

        self.log_text = tk.Text(
            log_frame,
            font=self.theme.FONTS['mono'],
            bg=self.theme.colors['bg_secondary'],
            fg=self.theme.colors['text_primary'],
            insertbackground=self.theme.colors['primary'],
            selectbackground=self.theme.colors['primary'],
            selectforeground='white',
            wrap=tk.WORD,
            state=tk.DISABLED,
            relief='flat',
            bd=0,
            padx=self.theme.SPACING['sm'],
            pady=self.theme.SPACING['sm']
        )

        scrollbar = tk.Scrollbar(
            log_frame,
            orient=tk.VERTICAL,
            command=self.log_text.yview,
            bg=self.theme.colors['bg_secondary'],
            troughcolor=self.theme.colors['bg_tertiary'],
            activebackground=self.theme.colors['primary']
        )
        self.log_text.configure(yscrollcommand=scrollbar.set)

        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def setup_stats_area(self, parent):
        """设置统计区域"""
        # 区域标题
        title_label = tk.Label(
            parent,
            text="📈 实时统计",
            font=self.theme.FONTS['heading'],
            fg=self.theme.colors['text_primary'],
            bg=self.theme.colors['bg_primary']
        )
        title_label.pack(anchor=tk.W, pady=(0, self.theme.SPACING['md']))

        # 统计卡片容器
        stats_container = tk.Frame(parent, bg=self.theme.colors['bg_primary'])
        stats_container.pack(fill=tk.X, pady=(0, self.theme.SPACING['lg']))

        # 创建统计卡片
        self.stats_cards = {}
        stats_config = [
            ('total', '总文件数', '0', '📄', 'primary'),
            ('success', '成功处理', '0', '✅', 'success'),
            ('failed', '处理失败', '0', '❌', 'error'),
            ('rate', '成功率', '0%', '📊', 'warning'),
        ]

        for i, (key, title, value, icon, color) in enumerate(stats_config):
            card = ModernStatCard(
                stats_container,
                self.theme,
                title=title,
                value=value,
                icon=icon,
                color=color
            )

            # 2x2网格布局
            row, col = divmod(i, 2)
            card.grid(row=row, column=col, padx=self.theme.SPACING['xs'],
                     pady=self.theme.SPACING['xs'], sticky='ew')

            self.stats_cards[key] = card

        # 配置网格权重
        stats_container.columnconfigure(0, weight=1)
        stats_container.columnconfigure(1, weight=1)

    def setup_progress_area(self, parent):
        """设置进度区域"""
        # 区域标题
        title_label = tk.Label(
            parent,
            text="⏳ 处理进度",
            font=self.theme.FONTS['heading'],
            fg=self.theme.colors['text_primary'],
            bg=self.theme.colors['bg_primary']
        )
        title_label.pack(anchor=tk.W, pady=(0, self.theme.SPACING['md']))

        # 进度卡片
        progress_card = ModernCard(parent, self.theme)
        progress_card.pack(fill=tk.X, pady=(0, self.theme.SPACING['lg']))

        # 状态标签
        self.status_label = tk.Label(
            progress_card,
            text="🟢 就绪",
            font=self.theme.FONTS['body'],
            fg=self.theme.colors['text_primary'],
            bg=self.theme.colors['surface']
        )
        self.status_label.pack(fill=tk.X, pady=(0, self.theme.SPACING['sm']))

        # 进度条
        self.progress_bar = ModernProgressBar(progress_card, self.theme)
        self.progress_bar.pack(fill=tk.X, pady=(0, self.theme.SPACING['sm']))

        # 详细信息
        self.detail_label = tk.Label(
            progress_card,
            text="",
            font=self.theme.FONTS['caption'],
            fg=self.theme.colors['text_muted'],
            bg=self.theme.colors['surface'],
            wraplength=300,
            justify=tk.LEFT
        )
        self.detail_label.pack(fill=tk.X)

    def setup_quick_settings(self, parent):
        """设置快速设置区域"""
        # 区域标题
        title_label = tk.Label(
            parent,
            text="⚙️ 快速设置",
            font=self.theme.FONTS['heading'],
            fg=self.theme.colors['text_primary'],
            bg=self.theme.colors['bg_primary']
        )
        title_label.pack(anchor=tk.W, pady=(0, self.theme.SPACING['md']))

        # 设置卡片
        settings_card = ModernCard(parent, self.theme)
        settings_card.pack(fill=tk.X)

        # 设置选项
        settings_frame = tk.Frame(settings_card, bg=self.theme.colors['surface'])
        settings_frame.pack(fill=tk.X, padx=self.theme.SPACING['sm'])

        # 并发处理开关
        self.parallel_var = tk.BooleanVar(value=config_manager.processing.enable_parallel)
        parallel_frame = tk.Frame(settings_frame, bg=self.theme.colors['surface'])
        parallel_frame.pack(fill=tk.X, pady=self.theme.SPACING['xs'])

        parallel_check = tk.Checkbutton(
            parallel_frame,
            text="🚀 启用并发处理",
            variable=self.parallel_var,
            font=self.theme.FONTS['body'],
            fg=self.theme.colors['text_primary'],
            bg=self.theme.colors['surface'],
            selectcolor=self.theme.colors['primary'],
            activebackground=self.theme.colors['surface'],
            activeforeground=self.theme.colors['text_primary'],
            command=self.update_settings
        )
        parallel_check.pack(side=tk.LEFT)

        # 备份开关
        self.backup_var = tk.BooleanVar(value=config_manager.processing.backup_enabled)
        backup_frame = tk.Frame(settings_frame, bg=self.theme.colors['surface'])
        backup_frame.pack(fill=tk.X, pady=self.theme.SPACING['xs'])

        backup_check = tk.Checkbutton(
            backup_frame,
            text="💾 启用文件备份",
            variable=self.backup_var,
            font=self.theme.FONTS['body'],
            fg=self.theme.colors['text_primary'],
            bg=self.theme.colors['surface'],
            selectcolor=self.theme.colors['primary'],
            activebackground=self.theme.colors['surface'],
            activeforeground=self.theme.colors['text_primary'],
            command=self.update_settings
        )
        backup_check.pack(side=tk.LEFT)

    def toggle_theme(self):
        """切换主题"""
        self.theme.toggle_theme()
        self.refresh_ui()
        self.log_message(f"🎨 切换到{'深色' if self.theme.dark_mode else '浅色'}主题")

    def refresh_ui(self):
        """刷新UI主题"""
        # 这里应该递归更新所有组件的颜色
        # 简化实现，实际需要更复杂的主题切换逻辑
        self.root.configure(bg=self.theme.colors['bg_primary'])
        messagebox.showinfo("主题切换", "主题已切换，重启应用以完全生效")

    def show_settings(self):
        """显示设置对话框"""
        messagebox.showinfo("设置", "高级设置功能开发中...")

    def on_folder_selected(self, folder_path: str):
        """文件夹选择回调"""
        self.current_folder = folder_path
        self.folder_path_label.configure(text=folder_path)
        self.log_message(f"📁 选择文件夹: {folder_path}")

        # 检查文件夹内容
        self.check_folder_content()

    def check_folder_content(self):
        """检查文件夹内容"""
        if not self.current_folder:
            return

        try:
            stats = self.pdf_extractor.get_pdf_statistics(self.current_folder)
            self.update_stats(stats)

            if stats['total_files'] > 0:
                self.log_message(f"✅ 发现 {stats['total_files']} 个PDF文件")
                self.status_label.configure(text="🟢 就绪")
                self.enable_action_buttons(True)
            else:
                self.log_message("⚠️ 未发现PDF文件")
                self.status_label.configure(text="🟡 无文件")
                self.enable_action_buttons(False)

        except Exception as e:
            self.log_message(f"❌ 检查文件失败: {str(e)}")
            self.status_label.configure(text="🔴 错误")
            self.enable_action_buttons(False)

    def update_stats(self, stats: Dict[str, Any]):
        """更新统计信息"""
        self.stats_cards['total'].update_value(str(stats.get('total_files', 0)))
        self.stats_cards['success'].update_value(str(stats.get('valid_files', 0)))
        self.stats_cards['failed'].update_value(str(stats.get('invalid_files', 0)))

        total = stats.get('total_files', 0)
        valid = stats.get('valid_files', 0)
        rate = f"{(valid/total*100):.1f}%" if total > 0 else "0%"
        self.stats_cards['rate'].update_value(rate)

    def enable_action_buttons(self, enabled: bool):
        """启用/禁用操作按钮"""
        state = tk.NORMAL if enabled else tk.DISABLED
        for btn in self.action_buttons:
            btn.configure(state=state)

    def log_message(self, message: str):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"

        self.log_text.configure(state=tk.NORMAL)
        self.log_text.insert(tk.END, formatted_message)
        self.log_text.see(tk.END)
        self.log_text.configure(state=tk.DISABLED)
        self.root.update()

    def update_settings(self):
        """更新设置"""
        config_manager.update_processing_config(
            enable_parallel=self.parallel_var.get(),
            backup_enabled=self.backup_var.get()
        )
        self.log_message("⚙️ 设置已更新")

    def update_progress(self, status: str, progress: int = None, detail: str = ""):
        """更新进度"""
        self.status_label.configure(text=status)
        if progress is not None:
            self.progress_bar.set_progress(progress)
        self.detail_label.configure(text=detail)
        self.root.update()

    def validate_folder(self) -> bool:
        """验证文件夹"""
        if not self.current_folder:
            messagebox.showwarning("警告", "请先选择包含PDF文件的文件夹")
            return False
        return True

    def start_processing(self, task_name: str, worker_func):
        """开始处理任务"""
        if self.processing:
            messagebox.showwarning("警告", "正在处理中，请稍候...")
            return

        self.processing = True
        self.enable_action_buttons(False)
        self.log_message(f"🚀 开始{task_name}...")

        # 在新线程中执行任务
        thread = threading.Thread(target=worker_func, daemon=True)
        thread.start()

    def full_process(self):
        """完整流程处理"""
        if not self.validate_folder():
            return

        if not messagebox.askyesno("确认",
                                  "确定要执行完整流程吗？\n\n"
                                  "这将包括：\n"
                                  "• 📄 提取PDF内容\n"
                                  "• 🤖 AI智能分析\n"
                                  "• 📝 文件重命名\n"
                                  "• 📂 店铺归类"):
            return

        self.start_processing("完整流程", self._full_process_worker)

    def rename_only(self):
        """仅重命名处理"""
        if not self.validate_folder():
            return

        if not messagebox.askyesno("确认", "确定要执行重命名操作吗？"):
            return

        self.start_processing("重命名处理", self._rename_only_worker)

    def classify_only(self):
        """仅归类处理"""
        if not self.validate_folder():
            return

        if not messagebox.askyesno("确认", "确定要执行店铺归类吗？"):
            return

        self.start_processing("店铺归类", self._classify_only_worker)

    def show_status(self):
        """显示文件状态"""
        if not self.validate_folder():
            return

        try:
            status = self.file_manager.get_file_status(self.current_folder)
            stats = self.pdf_extractor.get_pdf_statistics(self.current_folder)

            status_text = f"""📊 文件状态报告

📁 文件夹: {Path(self.current_folder).name}

📄 文件统计:
   • 总文件数: {stats['total_files']} 个
   • 行程单: {stats.get('itinerary_files', 0)} 个
   • 发票: {stats.get('invoice_files', 0)} 个
   • 有效文件: {stats.get('valid_files', 0)} 个
   • 无效文件: {stats.get('invalid_files', 0)} 个

🔄 处理状态:
   • 原始文件: {status['original_count']} 个
   • 已重命名: {status['renamed_count']} 个
   • 店铺归类: {'✅ 已完成' if status['has_classification'] else '❌ 未完成'}
"""

            messagebox.showinfo("文件状态", status_text)

        except Exception as e:
            messagebox.showerror("错误", f"获取状态失败: {str(e)}")

    def _full_process_worker(self):
        """完整流程工作线程"""
        try:
            # 1. 提取PDF内容
            self.message_queue.put(("progress", "🔍 正在提取PDF内容...", 10, "分析文件结构"))
            pdf_files = self.pdf_extractor.extract_pdf_content_parallel(self.current_folder)

            if not pdf_files:
                self.message_queue.put(("error", "未找到可处理的PDF文件"))
                return

            # 2. AI分析
            self.message_queue.put(("progress", "🤖 AI智能分析中...", 30, f"处理 {len(pdf_files)} 个文件"))
            ai_results = self.ai_analyzer.analyze_destinations_parallel(pdf_files)

            if not ai_results:
                self.message_queue.put(("error", "AI分析失败，请检查网络连接和API配置"))
                return

            # 3. 文件重命名
            self.message_queue.put(("progress", "📝 智能重命名中...", 60, "生成新文件名"))
            file_mappings = self.pdf_extractor.get_file_mappings(self.current_folder)
            success_count, errors = self.file_manager.execute_rename_with_lock(file_mappings, ai_results)

            if success_count == 0:
                self.message_queue.put(("error", "文件重命名失败"))
                return

            # 4. 店铺归类
            self.message_queue.put(("progress", "🏪 店铺智能归类中...", 80, "按店铺整理文件"))
            shop_data = self.file_manager.scan_renamed_files(self.current_folder)
            success, errors = self.file_manager.create_shop_classification(self.current_folder, shop_data)

            # 完成
            self.message_queue.put(("progress", "✅ 处理完成", 100, f"成功处理 {success_count} 个文件"))
            self.message_queue.put(("success", f"🎉 完整流程执行成功！\n\n"
                                             f"✅ 成功处理 {success_count} 个文件\n"
                                             f"📂 已按 {len(shop_data)} 个店铺归类\n"
                                             f"📋 详细报告已生成"))

        except Exception as e:
            self.message_queue.put(("error", f"处理失败: {str(e)}"))

    def _rename_only_worker(self):
        """重命名工作线程"""
        try:
            # 提取PDF内容
            self.message_queue.put(("progress", "🔍 提取PDF内容...", 20, ""))
            pdf_files = self.pdf_extractor.extract_pdf_content_parallel(self.current_folder)

            if not pdf_files:
                self.message_queue.put(("error", "未找到可处理的PDF文件"))
                return

            # AI分析
            self.message_queue.put(("progress", "🤖 AI分析中...", 60, ""))
            ai_results = self.ai_analyzer.analyze_destinations_parallel(pdf_files)

            if not ai_results:
                self.message_queue.put(("error", "AI分析失败"))
                return

            # 文件重命名
            self.message_queue.put(("progress", "📝 重命名文件...", 90, ""))
            file_mappings = self.pdf_extractor.get_file_mappings(self.current_folder)
            success_count, errors = self.file_manager.execute_rename_with_lock(file_mappings, ai_results)

            self.message_queue.put(("progress", "✅ 重命名完成", 100, f"成功处理 {success_count} 个文件"))
            self.message_queue.put(("success", f"📝 重命名操作完成！\n成功处理 {success_count} 个文件"))

        except Exception as e:
            self.message_queue.put(("error", f"重命名失败: {str(e)}"))

    def _classify_only_worker(self):
        """归类工作线程"""
        try:
            # 扫描已重命名文件
            self.message_queue.put(("progress", "📂 扫描文件...", 30, ""))
            shop_data = self.file_manager.scan_renamed_files(self.current_folder)

            if not shop_data:
                self.message_queue.put(("error", "未找到已重命名的文件"))
                return

            # 店铺归类
            self.message_queue.put(("progress", "🏪 店铺归类...", 80, ""))
            success, errors = self.file_manager.create_shop_classification(self.current_folder, shop_data)

            self.message_queue.put(("progress", "✅ 归类完成", 100, f"处理 {len(shop_data)} 个店铺"))
            self.message_queue.put(("success", f"📂 店铺归类完成！\n按 {len(shop_data)} 个店铺整理完毕"))

        except Exception as e:
            self.message_queue.put(("error", f"归类失败: {str(e)}"))

    def check_queue(self):
        """检查消息队列"""
        try:
            while True:
                message_type, *args = self.message_queue.get_nowait()

                if message_type == "progress":
                    status, progress, detail = args
                    self.update_progress(status, progress, detail)
                    self.log_message(status)

                elif message_type == "success":
                    message = args[0]
                    self.processing = False
                    self.enable_action_buttons(True)
                    self.update_progress("🟢 完成", 100, "所有任务已完成")
                    messagebox.showinfo("处理完成", message)
                    self.check_folder_content()  # 刷新统计

                elif message_type == "error":
                    message = args[0]
                    self.processing = False
                    self.enable_action_buttons(True)
                    self.update_progress("🔴 错误", 0, "处理过程中出现错误")
                    messagebox.showerror("处理失败", message)

        except queue.Empty:
            pass

        # 继续检查队列
        self.root.after(100, self.check_queue)

    def run(self):
        """运行应用"""
        self.log_message("🚀 PDF智能管理器 v2.0 已启动")
        self.log_message("💡 请选择包含PDF文件的文件夹开始处理")
        self.log_message("🎨 当前主题: " + ("深色模式" if self.theme.dark_mode else "浅色模式"))
        self.root.mainloop()


def main():
    """主函数"""
    try:
        app = ModernPDFManagerGUI()
        app.run()
    except Exception as e:
        messagebox.showerror("启动错误", f"应用启动失败: {str(e)}")


if __name__ == "__main__":
    main()
