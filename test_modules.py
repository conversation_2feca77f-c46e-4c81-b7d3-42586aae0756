#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模块测试脚本
验证重构后的各个模块是否正常工作
"""

import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_config_manager():
    """测试配置管理器"""
    print("🧪 测试配置管理器...")
    try:
        from config import config_manager, prompt_manager
        
        # 测试配置加载
        api_config = config_manager.api
        print(f"  ✅ API配置加载成功: {api_config.url}")
        
        proc_config = config_manager.processing
        print(f"  ✅ 处理配置加载成功: 并发数={proc_config.max_workers}")
        
        # 测试提示词管理
        prompt = prompt_manager.get_prompt("destination_extraction")
        print(f"  ✅ 提示词加载成功: {len(prompt)} 字符")
        
        # 测试配置验证
        is_valid = config_manager.validate_config()
        print(f"  ✅ 配置验证: {'通过' if is_valid else '失败'}")
        
        return True
    except Exception as e:
        print(f"  ❌ 配置管理器测试失败: {e}")
        return False

def test_pdf_extractor():
    """测试PDF提取器"""
    print("\n🧪 测试PDF提取器...")
    try:
        from pdf_extractor import PDFExtractor
        
        extractor = PDFExtractor()
        
        # 测试文件夹扫描
        folders = extractor.find_pdf_folders()
        print(f"  ✅ 文件夹扫描成功: 找到 {len(folders)} 个文件夹")
        
        # 测试金额提取
        test_filename = "250810_11.10_滴滴信息服务有限公司_行程单.pdf"
        amount = extractor.extract_amount_from_filename(test_filename)
        print(f"  ✅ 金额提取测试: {test_filename} -> {amount}")
        
        return True
    except Exception as e:
        print(f"  ❌ PDF提取器测试失败: {e}")
        return False

def test_ai_analyzer():
    """测试AI分析器"""
    print("\n🧪 测试AI分析器...")
    try:
        from ai_analyzer import AIAnalyzer
        
        analyzer = AIAnalyzer()
        print("  ✅ AI分析器初始化成功")
        
        # 测试响应验证（模拟）
        class MockResponse:
            def __init__(self, status_code, headers, text):
                self.status_code = status_code
                self.headers = headers
                self.text = text
            
            def json(self):
                if self.status_code == 200:
                    return {"choices": [{"message": {"content": "测试内容"}}]}
                raise ValueError("Invalid JSON")
        
        # 测试有效响应
        valid_response = MockResponse(200, {"content-type": "application/json"}, '{"choices":[]}')
        is_valid, message = analyzer._validate_api_response(valid_response)
        print(f"  ✅ 响应验证测试: {'通过' if is_valid else '失败'} - {message}")
        
        return True
    except Exception as e:
        print(f"  ❌ AI分析器测试失败: {e}")
        return False

def test_file_manager():
    """测试文件管理器"""
    print("\n🧪 测试文件管理器...")
    try:
        from file_manager import FileManager, FileLock
        
        manager = FileManager()
        
        # 测试文件名清理
        dirty_name = "测试<>店铺|名称?*.txt"
        clean_name = manager.clean_filename(dirty_name)
        print(f"  ✅ 文件名清理测试: '{dirty_name}' -> '{clean_name}'")
        
        # 测试店铺名提取
        destination = "龙华区|海底捞火锅(夏日广场店)"
        shop_name = manager.extract_shop_name(destination)
        print(f"  ✅ 店铺名提取测试: '{destination}' -> '{shop_name}'")
        
        # 测试文件锁（创建临时文件）
        import tempfile
        with tempfile.NamedTemporaryFile(delete=False) as tmp:
            tmp_path = tmp.name
        
        try:
            with FileLock(tmp_path):
                print("  ✅ 文件锁测试: 成功获取锁")
        except Exception as e:
            print(f"  ⚠️  文件锁测试: {e}")
        finally:
            Path(tmp_path).unlink(missing_ok=True)
        
        return True
    except Exception as e:
        print(f"  ❌ 文件管理器测试失败: {e}")
        return False

def test_ui_manager():
    """测试UI管理器"""
    print("\n🧪 测试UI管理器...")
    try:
        from ui_manager import UIManager, ProgressBar
        
        ui = UIManager()
        print("  ✅ UI管理器初始化成功")
        
        # 测试进度条
        import time
        progress = ProgressBar(5, "测试进度")
        for i in range(5):
            progress.update(1, f"步骤 {i+1}")
            time.sleep(0.1)
        progress.finish("测试完成")
        
        print("  ✅ 进度条测试完成")
        
        return True
    except Exception as e:
        print(f"  ❌ UI管理器测试失败: {e}")
        return False

def test_main_program():
    """测试主程序"""
    print("\n🧪 测试主程序...")
    try:
        from pdf_manager_v2 import PDFManagerV2
        
        # 只测试初始化，不运行主循环
        manager = PDFManagerV2()
        print("  ✅ 主程序初始化成功")
        
        return True
    except Exception as e:
        print(f"  ❌ 主程序测试失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("🚀 开始模块测试...")
    print("=" * 60)
    
    tests = [
        test_config_manager,
        test_pdf_extractor,
        test_ai_analyzer,
        test_file_manager,
        test_ui_manager,
        test_main_program
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！重构成功！")
        return True
    else:
        print("⚠️  部分测试失败，需要修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
