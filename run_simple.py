#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF智能管理器 - 简洁版启动脚本
"""

import sys
import tkinter as tk
from tkinter import messagebox
from pathlib import Path

def main():
    """主函数"""
    try:
        # 检查依赖
        try:
            import requests
            import PyPDF2
        except ImportError as e:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("依赖缺失", f"缺少依赖：{str(e)}\n\n请运行：pip install requests PyPDF2")
            return
        
        # 启动简洁GUI
        from simple_gui import SimplePDFManagerGUI
        
        app = SimplePDFManagerGUI()
        app.run()
        
    except Exception as e:
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("启动失败", str(e))

if __name__ == "__main__":
    main()
