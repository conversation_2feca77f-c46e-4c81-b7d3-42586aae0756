#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF智能管理器 - GUI版本
现代化、简洁、优美的图形界面
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import queue
import sys
from pathlib import Path
from typing import Optional, Dict, Any
import json

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from config import config_manager
from pdf_extractor import PDFExtractor
from ai_analyzer import AIAnalyzer
from file_manager import FileManager


class ModernStyle:
    """现代化UI样式配置"""
    
    # 颜色系统
    COLORS = {
        'primary': '#3B82F6',      # 蓝色主色调
        'primary_hover': '#2563EB',
        'secondary': '#6B7280',     # 灰色辅助色
        'success': '#10B981',       # 成功绿
        'warning': '#F59E0B',       # 警告橙
        'error': '#EF4444',         # 错误红
        'background': '#F9FAFB',    # 背景色
        'surface': '#FFFFFF',       # 表面色
        'text_primary': '#111827',  # 主文字色
        'text_secondary': '#6B7280', # 次要文字色
        'border': '#E5E7EB',        # 边框色
    }
    
    # 字体系统
    FONTS = {
        'title': ('Microsoft YaHei UI', 16, 'bold'),
        'heading': ('Microsoft YaHei UI', 12, 'bold'),
        'body': ('Microsoft YaHei UI', 10),
        'caption': ('Microsoft YaHei UI', 9),
        'button': ('Microsoft YaHei UI', 10, 'bold'),
    }
    
    # 间距系统
    SPACING = {
        'xs': 4, 'sm': 8, 'md': 16, 'lg': 24, 'xl': 32
    }


class ProgressDialog:
    """进度对话框"""
    
    def __init__(self, parent, title="处理中..."):
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("400x150")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.dialog.geometry("+%d+%d" % (
            parent.winfo_rootx() + 50,
            parent.winfo_rooty() + 50
        ))
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置UI"""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 状态标签
        self.status_label = ttk.Label(
            main_frame, 
            text="准备开始...",
            font=ModernStyle.FONTS['body']
        )
        self.status_label.pack(pady=(0, 10))
        
        # 进度条
        self.progress = ttk.Progressbar(
            main_frame,
            mode='determinate',
            length=350
        )
        self.progress.pack(pady=(0, 10))
        
        # 详细信息
        self.detail_label = ttk.Label(
            main_frame,
            text="",
            font=ModernStyle.FONTS['caption'],
            foreground=ModernStyle.COLORS['text_secondary']
        )
        self.detail_label.pack()
        
    def update(self, status: str, progress: int = None, detail: str = ""):
        """更新进度"""
        self.status_label.config(text=status)
        if progress is not None:
            self.progress['value'] = progress
        self.detail_label.config(text=detail)
        self.dialog.update()
        
    def close(self):
        """关闭对话框"""
        self.dialog.destroy()


class StatCard:
    """统计卡片组件"""
    
    def __init__(self, parent, title: str, value: str = "0", color: str = "primary"):
        self.frame = ttk.Frame(parent, style="Card.TFrame")
        self.color = color
        self.setup_ui(title, value)
        
    def setup_ui(self, title: str, value: str):
        """设置UI"""
        # 标题
        title_label = ttk.Label(
            self.frame,
            text=title,
            font=ModernStyle.FONTS['caption'],
            foreground=ModernStyle.COLORS['text_secondary']
        )
        title_label.pack(pady=(8, 2))
        
        # 数值
        self.value_label = ttk.Label(
            self.frame,
            text=value,
            font=ModernStyle.FONTS['title'],
            foreground=ModernStyle.COLORS[self.color]
        )
        self.value_label.pack(pady=(0, 8))
        
    def update_value(self, value: str):
        """更新数值"""
        self.value_label.config(text=value)
        
    def pack(self, **kwargs):
        """包装pack方法"""
        self.frame.pack(**kwargs)
        
    def grid(self, **kwargs):
        """包装grid方法"""
        self.frame.grid(**kwargs)


class PDFManagerGUI:
    """PDF管理器GUI主类"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.setup_styles()
        self.setup_ui()
        
        # 业务逻辑组件
        self.pdf_extractor = PDFExtractor()
        self.ai_analyzer = AIAnalyzer()
        self.file_manager = FileManager()
        
        # 状态变量
        self.current_folder = None
        self.processing = False
        
        # 消息队列用于线程通信
        self.message_queue = queue.Queue()
        self.check_queue()
        
    def setup_window(self):
        """设置主窗口"""
        self.root.title("PDF智能管理器 v2.0")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)
        
        # 设置图标（如果有的话）
        try:
            # self.root.iconbitmap("icon.ico")
            pass
        except:
            pass
            
        # 居中显示
        self.center_window()
        
    def center_window(self):
        """窗口居中"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
        
    def setup_styles(self):
        """设置样式"""
        style = ttk.Style()
        
        # 配置主题
        style.theme_use('clam')
        
        # 自定义样式
        style.configure("Title.TLabel", font=ModernStyle.FONTS['title'])
        style.configure("Heading.TLabel", font=ModernStyle.FONTS['heading'])
        style.configure("Card.TFrame", relief="solid", borderwidth=1)
        
        # 按钮样式
        style.configure(
            "Primary.TButton",
            font=ModernStyle.FONTS['button'],
            padding=(16, 8)
        )
        
        style.configure(
            "Success.TButton",
            font=ModernStyle.FONTS['button'],
            padding=(16, 8)
        )
        
    def setup_ui(self):
        """设置用户界面"""
        # 主容器
        main_container = ttk.Frame(self.root)
        main_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题区域
        self.setup_header(main_container)
        
        # 内容区域
        content_frame = ttk.Frame(main_container)
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(20, 0))
        
        # 左侧面板
        self.setup_left_panel(content_frame)
        
        # 右侧面板
        self.setup_right_panel(content_frame)
        
    def setup_header(self, parent):
        """设置标题区域"""
        header_frame = ttk.Frame(parent)
        header_frame.pack(fill=tk.X)
        
        # 标题
        title_label = ttk.Label(
            header_frame,
            text="📄 PDF智能管理器",
            style="Title.TLabel"
        )
        title_label.pack(side=tk.LEFT)
        
        # 版本信息
        version_label = ttk.Label(
            header_frame,
            text="v2.0",
            font=ModernStyle.FONTS['caption'],
            foreground=ModernStyle.COLORS['text_secondary']
        )
        version_label.pack(side=tk.LEFT, padx=(10, 0))
        
        # 状态指示器
        self.status_label = ttk.Label(
            header_frame,
            text="🟢 就绪",
            font=ModernStyle.FONTS['body']
        )
        self.status_label.pack(side=tk.RIGHT)
        
    def setup_left_panel(self, parent):
        """设置左侧面板"""
        left_frame = ttk.Frame(parent)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # 文件选择区域
        self.setup_file_selection(left_frame)
        
        # 功能按钮区域
        self.setup_action_buttons(left_frame)
        
        # 日志区域
        self.setup_log_area(left_frame)
        
    def setup_right_panel(self, parent):
        """设置右侧面板"""
        right_frame = ttk.Frame(parent, width=300)
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 0))
        right_frame.pack_propagate(False)
        
        # 统计卡片区域
        self.setup_stats_area(right_frame)
        
        # 配置区域
        self.setup_config_area(right_frame)
        
    def setup_file_selection(self, parent):
        """设置文件选择区域"""
        # 标题
        ttk.Label(
            parent,
            text="📁 选择文件夹",
            style="Heading.TLabel"
        ).pack(anchor=tk.W, pady=(0, 10))
        
        # 文件夹选择框
        folder_frame = ttk.Frame(parent)
        folder_frame.pack(fill=tk.X, pady=(0, 20))
        
        self.folder_var = tk.StringVar(value="请选择包含PDF文件的文件夹...")
        self.folder_entry = ttk.Entry(
            folder_frame,
            textvariable=self.folder_var,
            state="readonly",
            font=ModernStyle.FONTS['body']
        )
        self.folder_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        browse_btn = ttk.Button(
            folder_frame,
            text="浏览",
            command=self.browse_folder,
            style="Primary.TButton"
        )
        browse_btn.pack(side=tk.RIGHT, padx=(10, 0))
        
    def setup_action_buttons(self, parent):
        """设置功能按钮区域"""
        # 标题
        ttk.Label(
            parent,
            text="🚀 处理功能",
            style="Heading.TLabel"
        ).pack(anchor=tk.W, pady=(0, 10))
        
        # 按钮容器
        buttons_frame = ttk.Frame(parent)
        buttons_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 按钮配置
        buttons = [
            ("🔄 完整流程", self.full_process, "Primary.TButton"),
            ("📝 仅重命名", self.rename_only, "Primary.TButton"),
            ("📂 仅归类", self.classify_only, "Primary.TButton"),
            ("📊 查看状态", self.show_status, "Primary.TButton"),
        ]
        
        for i, (text, command, style) in enumerate(buttons):
            btn = ttk.Button(
                buttons_frame,
                text=text,
                command=command,
                style=style,
                width=15
            )
            row, col = divmod(i, 2)
            btn.grid(row=row, column=col, padx=5, pady=5, sticky="ew")
            
        buttons_frame.columnconfigure(0, weight=1)
        buttons_frame.columnconfigure(1, weight=1)
        
    def setup_log_area(self, parent):
        """设置日志区域"""
        # 标题
        ttk.Label(
            parent,
            text="📋 处理日志",
            style="Heading.TLabel"
        ).pack(anchor=tk.W, pady=(0, 10))
        
        # 日志文本框
        log_frame = ttk.Frame(parent)
        log_frame.pack(fill=tk.BOTH, expand=True)
        
        self.log_text = tk.Text(
            log_frame,
            height=10,
            font=ModernStyle.FONTS['body'],
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        
        scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
    def setup_stats_area(self, parent):
        """设置统计区域"""
        # 标题
        ttk.Label(
            parent,
            text="📈 处理统计",
            style="Heading.TLabel"
        ).pack(anchor=tk.W, pady=(0, 10))
        
        # 统计卡片
        stats_frame = ttk.Frame(parent)
        stats_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 创建统计卡片
        self.stats = {
            'total': StatCard(stats_frame, "总文件数", "0", "primary"),
            'success': StatCard(stats_frame, "成功处理", "0", "success"),
            'failed': StatCard(stats_frame, "处理失败", "0", "error"),
            'rate': StatCard(stats_frame, "成功率", "0%", "warning")
        }
        
        # 布局统计卡片
        for i, (key, card) in enumerate(self.stats.items()):
            row, col = divmod(i, 2)
            card.grid(row=row, column=col, padx=2, pady=2, sticky="ew")
            
        stats_frame.columnconfigure(0, weight=1)
        stats_frame.columnconfigure(1, weight=1)
        
    def setup_config_area(self, parent):
        """设置配置区域"""
        # 标题
        ttk.Label(
            parent,
            text="⚙️ 快速配置",
            style="Heading.TLabel"
        ).pack(anchor=tk.W, pady=(0, 10))
        
        config_frame = ttk.Frame(parent)
        config_frame.pack(fill=tk.X)
        
        # 并发处理开关
        self.parallel_var = tk.BooleanVar(value=config_manager.processing.enable_parallel)
        parallel_check = ttk.Checkbutton(
            config_frame,
            text="启用并发处理",
            variable=self.parallel_var,
            command=self.update_config
        )
        parallel_check.pack(anchor=tk.W, pady=2)
        
        # 备份开关
        self.backup_var = tk.BooleanVar(value=config_manager.processing.backup_enabled)
        backup_check = ttk.Checkbutton(
            config_frame,
            text="启用文件备份",
            variable=self.backup_var,
            command=self.update_config
        )
        backup_check.pack(anchor=tk.W, pady=2)
        
        # 配置按钮
        config_btn = ttk.Button(
            config_frame,
            text="高级配置",
            command=self.show_advanced_config,
            style="Primary.TButton"
        )
        config_btn.pack(fill=tk.X, pady=(10, 0))

    def browse_folder(self):
        """浏览文件夹"""
        folder = filedialog.askdirectory(title="选择包含PDF文件的文件夹")
        if folder:
            self.current_folder = folder
            self.folder_var.set(folder)
            self.log_message(f"📁 选择文件夹: {folder}")

            # 检查文件夹中的PDF文件
            self.check_folder_files()

    def check_folder_files(self):
        """检查文件夹中的文件"""
        if not self.current_folder:
            return

        try:
            stats = self.pdf_extractor.get_pdf_statistics(self.current_folder)
            self.update_stats(stats)

            if stats['total_files'] > 0:
                self.log_message(f"✅ 发现 {stats['total_files']} 个PDF文件")
                self.status_label.config(text="🟢 就绪")
            else:
                self.log_message("⚠️ 未发现PDF文件")
                self.status_label.config(text="🟡 无文件")

        except Exception as e:
            self.log_message(f"❌ 检查文件失败: {str(e)}")
            self.status_label.config(text="🔴 错误")

    def update_stats(self, stats: Dict[str, Any]):
        """更新统计信息"""
        self.stats['total'].update_value(str(stats.get('total_files', 0)))
        self.stats['success'].update_value(str(stats.get('valid_files', 0)))
        self.stats['failed'].update_value(str(stats.get('invalid_files', 0)))

        total = stats.get('total_files', 0)
        valid = stats.get('valid_files', 0)
        rate = f"{(valid/total*100):.1f}%" if total > 0 else "0%"
        self.stats['rate'].update_value(rate)

    def log_message(self, message: str):
        """添加日志消息"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)
        self.root.update()

    def update_config(self):
        """更新配置"""
        config_manager.update_processing_config(
            enable_parallel=self.parallel_var.get(),
            backup_enabled=self.backup_var.get()
        )
        self.log_message("⚙️ 配置已更新")

    def show_advanced_config(self):
        """显示高级配置对话框"""
        messagebox.showinfo("高级配置", "高级配置功能开发中...")

    def full_process(self):
        """完整流程处理"""
        if not self.validate_folder():
            return

        if self.processing:
            messagebox.showwarning("警告", "正在处理中，请稍候...")
            return

        # 确认对话框
        if not messagebox.askyesno("确认", "确定要执行完整流程吗？\n这将包括：提取→分析→重命名→归类"):
            return

        self.start_processing("完整流程", self._full_process_worker)

    def rename_only(self):
        """仅重命名处理"""
        if not self.validate_folder():
            return

        if self.processing:
            messagebox.showwarning("警告", "正在处理中，请稍候...")
            return

        if not messagebox.askyesno("确认", "确定要执行重命名操作吗？"):
            return

        self.start_processing("重命名处理", self._rename_only_worker)

    def classify_only(self):
        """仅归类处理"""
        if not self.validate_folder():
            return

        if self.processing:
            messagebox.showwarning("警告", "正在处理中，请稍候...")
            return

        if not messagebox.askyesno("确认", "确定要执行店铺归类吗？"):
            return

        self.start_processing("店铺归类", self._classify_only_worker)

    def show_status(self):
        """显示文件状态"""
        if not self.validate_folder():
            return

        try:
            status = self.file_manager.get_file_status(self.current_folder)
            stats = self.pdf_extractor.get_pdf_statistics(self.current_folder)

            status_text = f"""文件状态报告:

📄 原始文件: {status['original_count']} 个
✏️ 已重命名: {status['renamed_count']} 个
📁 店铺归类: {'✅ 已完成' if status['has_classification'] else '❌ 未完成'}

📊 文件统计:
📄 总文件数: {stats['total_files']}
📋 行程单: {stats.get('itinerary_files', 0)}
🧾 发票: {stats.get('invoice_files', 0)}
✅ 有效文件: {stats.get('valid_files', 0)}
❌ 无效文件: {stats.get('invalid_files', 0)}
"""

            messagebox.showinfo("文件状态", status_text)

        except Exception as e:
            messagebox.showerror("错误", f"获取状态失败: {str(e)}")

    def validate_folder(self) -> bool:
        """验证文件夹"""
        if not self.current_folder:
            messagebox.showwarning("警告", "请先选择文件夹")
            return False
        return True

    def start_processing(self, task_name: str, worker_func):
        """开始处理任务"""
        self.processing = True
        self.status_label.config(text="🔄 处理中")

        # 创建进度对话框
        self.progress_dialog = ProgressDialog(self.root, f"{task_name}中...")

        # 在新线程中执行任务
        thread = threading.Thread(target=worker_func, daemon=True)
        thread.start()

    def _full_process_worker(self):
        """完整流程工作线程"""
        try:
            # 1. 提取PDF内容
            self.message_queue.put(("progress", "🔍 提取PDF内容...", 10, ""))
            pdf_files = self.pdf_extractor.extract_pdf_content_parallel(self.current_folder)

            if not pdf_files:
                self.message_queue.put(("error", "未找到可处理的PDF文件"))
                return

            # 2. AI分析
            self.message_queue.put(("progress", "🤖 AI分析中...", 30, f"处理 {len(pdf_files)} 个文件"))
            ai_results = self.ai_analyzer.analyze_destinations_parallel(pdf_files)

            if not ai_results:
                self.message_queue.put(("error", "AI分析失败"))
                return

            # 3. 文件重命名
            self.message_queue.put(("progress", "📝 重命名文件...", 60, ""))
            file_mappings = self.pdf_extractor.get_file_mappings(self.current_folder)
            success_count, errors = self.file_manager.execute_rename_with_lock(file_mappings, ai_results)

            if success_count == 0:
                self.message_queue.put(("error", "重命名失败"))
                return

            # 4. 店铺归类
            self.message_queue.put(("progress", "🏪 店铺归类...", 80, ""))
            shop_data = self.file_manager.scan_renamed_files(self.current_folder)
            success, errors = self.file_manager.create_shop_classification(self.current_folder, shop_data)

            # 完成
            self.message_queue.put(("progress", "✅ 处理完成", 100, f"成功处理 {success_count} 个文件"))
            self.message_queue.put(("success", "完整流程执行成功！"))

        except Exception as e:
            self.message_queue.put(("error", f"处理失败: {str(e)}"))

    def _rename_only_worker(self):
        """重命名工作线程"""
        try:
            # 提取PDF内容
            self.message_queue.put(("progress", "🔍 提取PDF内容...", 20, ""))
            pdf_files = self.pdf_extractor.extract_pdf_content_parallel(self.current_folder)

            if not pdf_files:
                self.message_queue.put(("error", "未找到可处理的PDF文件"))
                return

            # AI分析
            self.message_queue.put(("progress", "🤖 AI分析中...", 60, ""))
            ai_results = self.ai_analyzer.analyze_destinations_parallel(pdf_files)

            if not ai_results:
                self.message_queue.put(("error", "AI分析失败"))
                return

            # 文件重命名
            self.message_queue.put(("progress", "📝 重命名文件...", 90, ""))
            file_mappings = self.pdf_extractor.get_file_mappings(self.current_folder)
            success_count, errors = self.file_manager.execute_rename_with_lock(file_mappings, ai_results)

            self.message_queue.put(("progress", "✅ 重命名完成", 100, f"成功处理 {success_count} 个文件"))
            self.message_queue.put(("success", "重命名操作完成！"))

        except Exception as e:
            self.message_queue.put(("error", f"重命名失败: {str(e)}"))

    def _classify_only_worker(self):
        """归类工作线程"""
        try:
            # 扫描已重命名文件
            self.message_queue.put(("progress", "📂 扫描文件...", 30, ""))
            shop_data = self.file_manager.scan_renamed_files(self.current_folder)

            if not shop_data:
                self.message_queue.put(("error", "未找到已重命名的文件"))
                return

            # 店铺归类
            self.message_queue.put(("progress", "🏪 店铺归类...", 80, ""))
            success, errors = self.file_manager.create_shop_classification(self.current_folder, shop_data)

            self.message_queue.put(("progress", "✅ 归类完成", 100, f"处理 {len(shop_data)} 个店铺"))
            self.message_queue.put(("success", "店铺归类完成！"))

        except Exception as e:
            self.message_queue.put(("error", f"归类失败: {str(e)}"))

    def check_queue(self):
        """检查消息队列"""
        try:
            while True:
                message_type, *args = self.message_queue.get_nowait()

                if message_type == "progress":
                    status, progress, detail = args
                    if hasattr(self, 'progress_dialog'):
                        self.progress_dialog.update(status, progress, detail)
                    self.log_message(status)

                elif message_type == "success":
                    message = args[0]
                    self.processing = False
                    self.status_label.config(text="🟢 完成")
                    if hasattr(self, 'progress_dialog'):
                        self.progress_dialog.close()
                    messagebox.showinfo("成功", message)
                    self.check_folder_files()  # 刷新统计

                elif message_type == "error":
                    message = args[0]
                    self.processing = False
                    self.status_label.config(text="🔴 错误")
                    if hasattr(self, 'progress_dialog'):
                        self.progress_dialog.close()
                    messagebox.showerror("错误", message)

        except queue.Empty:
            pass

        # 继续检查队列
        self.root.after(100, self.check_queue)

    def run(self):
        """运行应用"""
        self.log_message("🚀 PDF智能管理器已启动")
        self.log_message("📁 请选择包含PDF文件的文件夹开始处理")
        self.root.mainloop()


def main():
    """主函数"""
    try:
        app = PDFManagerGUI()
        app.run()
    except Exception as e:
        messagebox.showerror("启动错误", f"应用启动失败: {str(e)}")


if __name__ == "__main__":
    main()
