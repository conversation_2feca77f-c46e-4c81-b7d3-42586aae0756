#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现代化GUI演示脚本
展示现代化设计组件和特性
"""

import tkinter as tk
from tkinter import messagebox
import time
import threading

# 简化版的现代化组件演示
class ModernThemeDemo:
    """现代化主题演示"""
    
    DARK_THEME = {
        'bg_primary': '#0F172A',
        'bg_secondary': '#1E293B', 
        'surface': '#475569',
        'primary': '#3B82F6',
        'success': '#10B981',
        'warning': '#F59E0B',
        'error': '#EF4444',
        'text_primary': '#F8FAFC',
        'text_secondary': '#CBD5E1',
    }
    
    def __init__(self):
        self.colors = self.DARK_THEME

class ModernButtonDemo(tk.Button):
    """现代化按钮演示"""
    
    def __init__(self, parent, theme, text="", variant='primary', **kwargs):
        self.theme = theme
        self.variant = variant
        
        color_map = {
            'primary': theme.colors['primary'],
            'success': theme.colors['success'],
            'warning': theme.colors['warning'],
            'error': theme.colors['error']
        }
        
        bg_color = color_map.get(variant, theme.colors['primary'])
        
        super().__init__(
            parent,
            text=text,
            font=('Segoe UI', 10, 'bold'),
            bg=bg_color,
            fg='white',
            activebackground=bg_color,
            activeforeground='white',
            relief='flat',
            bd=0,
            cursor='hand2',
            padx=20,
            pady=10,
            **kwargs
        )
        
        self.bind('<Enter>', lambda e: self.configure(bg=self._get_hover_color()))
        self.bind('<Leave>', lambda e: self.configure(bg=bg_color))
    
    def _get_hover_color(self):
        """获取悬停颜色"""
        hover_map = {
            'primary': '#2563EB',
            'success': '#059669', 
            'warning': '#D97706',
            'error': '#DC2626'
        }
        return hover_map.get(self.variant, '#2563EB')

class ModernCardDemo(tk.Frame):
    """现代化卡片演示"""
    
    def __init__(self, parent, theme, title="", **kwargs):
        super().__init__(
            parent,
            bg=theme.colors['surface'],
            relief='flat',
            bd=1,
            highlightbackground=theme.colors['bg_secondary'],
            highlightthickness=1,
            padx=20,
            pady=15,
            **kwargs
        )
        
        if title:
            title_label = tk.Label(
                self,
                text=title,
                font=('Segoe UI', 12, 'bold'),
                fg=theme.colors['text_primary'],
                bg=theme.colors['surface']
            )
            title_label.pack(anchor='w', pady=(0, 10))

class ModernGUIDemo:
    """现代化GUI演示应用"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.theme = ModernThemeDemo()
        self.setup_window()
        self.setup_ui()
        
    def setup_window(self):
        """设置窗口"""
        self.root.title("🚀 PDF智能管理器 v2.0 - 现代化GUI演示")
        self.root.geometry("1000x700")
        self.root.configure(bg=self.theme.colors['bg_primary'])
        
        # 居中显示
        self.center_window()
    
    def center_window(self):
        """窗口居中"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def setup_ui(self):
        """设置UI"""
        # 主容器
        main_container = tk.Frame(
            self.root,
            bg=self.theme.colors['bg_primary'],
            padx=30,
            pady=30
        )
        main_container.pack(fill=tk.BOTH, expand=True)
        
        # 标题区域
        self.setup_header(main_container)
        
        # 内容区域
        self.setup_content(main_container)
    
    def setup_header(self, parent):
        """设置标题区域"""
        header_frame = tk.Frame(parent, bg=self.theme.colors['bg_primary'])
        header_frame.pack(fill=tk.X, pady=(0, 30))
        
        # 主标题
        title_label = tk.Label(
            header_frame,
            text="🚀 PDF智能管理器 v2.0",
            font=('Segoe UI', 24, 'bold'),
            fg=self.theme.colors['text_primary'],
            bg=self.theme.colors['bg_primary']
        )
        title_label.pack(side=tk.LEFT)
        
        # 副标题
        subtitle_label = tk.Label(
            header_frame,
            text="现代化GUI演示",
            font=('Segoe UI', 12),
            fg=self.theme.colors['text_secondary'],
            bg=self.theme.colors['bg_primary']
        )
        subtitle_label.pack(side=tk.LEFT, padx=(20, 0))
        
        # 状态指示器
        status_label = tk.Label(
            header_frame,
            text="🟢 演示模式",
            font=('Segoe UI', 10, 'bold'),
            fg=self.theme.colors['success'],
            bg=self.theme.colors['bg_primary']
        )
        status_label.pack(side=tk.RIGHT)
    
    def setup_content(self, parent):
        """设置内容区域"""
        content_frame = tk.Frame(parent, bg=self.theme.colors['bg_primary'])
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # 左侧面板
        left_panel = tk.Frame(content_frame, bg=self.theme.colors['bg_primary'])
        left_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 15))
        
        # 功能演示卡片
        self.setup_feature_demo(left_panel)
        
        # 按钮演示卡片
        self.setup_button_demo(left_panel)
        
        # 右侧面板
        right_panel = tk.Frame(content_frame, bg=self.theme.colors['bg_primary'], width=300)
        right_panel.pack(side=tk.RIGHT, fill=tk.Y)
        right_panel.pack_propagate(False)
        
        # 统计演示卡片
        self.setup_stats_demo(right_panel)
        
        # 日志演示卡片
        self.setup_log_demo(right_panel)
    
    def setup_feature_demo(self, parent):
        """设置功能演示"""
        feature_card = ModernCardDemo(parent, self.theme, "✨ 现代化特性展示")
        feature_card.pack(fill=tk.X, pady=(0, 20))
        
        features = [
            "🎨 深色主题设计，护眼舒适",
            "📱 响应式卡片布局，信息清晰",
            "⚡ 并发处理技术，性能提升3-4倍",
            "🤖 AI智能分析，准确识别终点信息",
            "📊 实时统计展示，处理进度可视化",
            "🔒 文件锁机制，防止并发冲突",
            "💾 自动备份功能，数据安全保障"
        ]
        
        for feature in features:
            feature_label = tk.Label(
                feature_card,
                text=feature,
                font=('Segoe UI', 10),
                fg=self.theme.colors['text_primary'],
                bg=self.theme.colors['surface'],
                anchor='w'
            )
            feature_label.pack(fill=tk.X, pady=2)
    
    def setup_button_demo(self, parent):
        """设置按钮演示"""
        button_card = ModernCardDemo(parent, self.theme, "🎯 操作按钮演示")
        button_card.pack(fill=tk.X, pady=(0, 20))
        
        button_frame = tk.Frame(button_card, bg=self.theme.colors['surface'])
        button_frame.pack(fill=tk.X, pady=10)
        
        # 按钮配置
        buttons = [
            ("🔄 完整流程", "primary", self.demo_full_process),
            ("📝 仅重命名", "success", self.demo_rename),
            ("📂 仅归类", "warning", self.demo_classify),
            ("📊 查看状态", "error", self.demo_status)
        ]
        
        for i, (text, variant, command) in enumerate(buttons):
            btn = ModernButtonDemo(
                button_frame,
                self.theme,
                text=text,
                variant=variant,
                command=command
            )
            row, col = divmod(i, 2)
            btn.grid(row=row, column=col, padx=5, pady=5, sticky='ew')
        
        button_frame.columnconfigure(0, weight=1)
        button_frame.columnconfigure(1, weight=1)
    
    def setup_stats_demo(self, parent):
        """设置统计演示"""
        stats_card = ModernCardDemo(parent, self.theme, "📈 实时统计演示")
        stats_card.pack(fill=tk.X, pady=(0, 20))
        
        # 统计项目
        stats = [
            ("📄 总文件数", "18", self.theme.colors['primary']),
            ("✅ 成功处理", "18", self.theme.colors['success']),
            ("❌ 处理失败", "0", self.theme.colors['error']),
            ("📊 成功率", "100%", self.theme.colors['warning'])
        ]
        
        for title, value, color in stats:
            stat_frame = tk.Frame(stats_card, bg=self.theme.colors['surface'])
            stat_frame.pack(fill=tk.X, pady=5)
            
            title_label = tk.Label(
                stat_frame,
                text=title,
                font=('Segoe UI', 9),
                fg=self.theme.colors['text_secondary'],
                bg=self.theme.colors['surface']
            )
            title_label.pack(side=tk.LEFT)
            
            value_label = tk.Label(
                stat_frame,
                text=value,
                font=('Segoe UI', 12, 'bold'),
                fg=color,
                bg=self.theme.colors['surface']
            )
            value_label.pack(side=tk.RIGHT)
    
    def setup_log_demo(self, parent):
        """设置日志演示"""
        log_card = ModernCardDemo(parent, self.theme, "📋 处理日志演示")
        log_card.pack(fill=tk.BOTH, expand=True)
        
        # 日志文本区域
        self.log_text = tk.Text(
            log_card,
            font=('Consolas', 9),
            bg=self.theme.colors['bg_secondary'],
            fg=self.theme.colors['text_primary'],
            insertbackground=self.theme.colors['primary'],
            wrap=tk.WORD,
            state=tk.DISABLED,
            relief='flat',
            bd=0,
            padx=10,
            pady=10
        )
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # 添加演示日志
        demo_logs = [
            "🚀 PDF智能管理器 v2.0 已启动",
            "📁 选择文件夹: QQ邮箱发票_共18张_总金额780.10元",
            "✅ 发现 18 个PDF文件",
            "🔍 开始提取PDF内容...",
            "🤖 AI智能分析中...",
            "📝 智能重命名中...",
            "🏪 店铺智能归类中...",
            "✅ 处理完成！成功处理 36 个文件",
            "📂 已按 15 个店铺归类",
            "📋 详细报告已生成"
        ]
        
        self.log_text.configure(state=tk.NORMAL)
        for log in demo_logs:
            timestamp = time.strftime("%H:%M:%S")
            self.log_text.insert(tk.END, f"[{timestamp}] {log}\n")
        self.log_text.configure(state=tk.DISABLED)
    
    def demo_full_process(self):
        """演示完整流程"""
        messagebox.showinfo(
            "完整流程演示",
            "🔄 完整流程包括：\n\n"
            "1. 📄 提取PDF内容\n"
            "2. 🤖 AI智能分析\n"
            "3. 📝 文件重命名\n"
            "4. 📂 店铺归类\n\n"
            "✨ 现代化特性：\n"
            "• 并发处理提升速度\n"
            "• 实时进度显示\n"
            "• 智能错误重试\n"
            "• 自动备份保护"
        )
    
    def demo_rename(self):
        """演示重命名"""
        messagebox.showinfo(
            "重命名演示", 
            "📝 智能重命名功能：\n\n"
            "• AI分析终点信息\n"
            "• 自动生成规范文件名\n"
            "• 文件锁防止冲突\n"
            "• 支持批量处理"
        )
    
    def demo_classify(self):
        """演示归类"""
        messagebox.showinfo(
            "归类演示",
            "📂 智能店铺归类：\n\n"
            "• 自动识别店铺名称\n"
            "• 按店铺创建文件夹\n"
            "• 生成统计报告\n"
            "• 便于报销管理"
        )
    
    def demo_status(self):
        """演示状态"""
        messagebox.showinfo(
            "状态演示",
            "📊 文件状态查看：\n\n"
            "📄 总文件数: 18 个\n"
            "📋 行程单: 18 个\n"
            "🧾 发票: 18 个\n"
            "✅ 有效文件: 18 个\n"
            "❌ 无效文件: 0 个\n\n"
            "🔄 处理状态:\n"
            "• 已重命名: 18 个\n"
            "• 店铺归类: ✅ 已完成"
        )
    
    def run(self):
        """运行演示"""
        self.root.mainloop()

def main():
    """主函数"""
    try:
        demo = ModernGUIDemo()
        demo.run()
    except Exception as e:
        messagebox.showerror("演示错误", f"演示启动失败: {str(e)}")

if __name__ == "__main__":
    main()
