#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF提取器模块
负责PDF文件的内容提取和并发处理
"""

import os
import re
import glob
import logging
from typing import Dict, List, Optional, Tuple
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass
from PyPDF2 import PdfReader
import threading

from config import config_manager


@dataclass
class PDFFile:
    """PDF文件信息"""
    path: str
    filename: str
    amount: Optional[str] = None
    content: Optional[str] = None
    error: Optional[str] = None


class PDFExtractor:
    """PDF内容提取器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.config = config_manager.processing
        self._lock = threading.Lock()
        
    def find_pdf_folders(self, base_path: str = None) -> List[Dict]:
        """自动识别包含PDF文件的文件夹"""
        if base_path is None:
            base_path = os.getcwd()
        
        base_path = Path(base_path)
        pdf_folders = []
        
        try:
            # 检查当前目录
            pdf_files = list(base_path.glob("*.pdf"))
            if pdf_files:
                pdf_folders.append({
                    'path': str(base_path),
                    'name': '当前目录',
                    'count': len(pdf_files)
                })
            
            # 检查子目录
            for item in base_path.iterdir():
                if item.is_dir() and not item.name.startswith('.'):
                    pdf_files = list(item.glob("*.pdf"))
                    if pdf_files:
                        pdf_folders.append({
                            'path': str(item),
                            'name': item.name,
                            'count': len(pdf_files)
                        })
                        
        except Exception as e:
            self.logger.error(f"扫描PDF文件夹失败: {e}")
            
        return pdf_folders
    
    def extract_amount_from_filename(self, filename: str) -> Optional[str]:
        """从文件名中提取金额"""
        try:
            # 支持多种文件名格式
            patterns = [
                r'^\d{6}_(\d+\.?\d*)_.*\.pdf$',  # 250810_11.10_公司名_行程单.pdf
                r'-(\d+\.?\d*)-\d{4}年\d{2}月\d{2}日\.pdf$',  # 原格式
                r'_(\d+\.?\d*)_.*\.pdf$',  # 通用格式
            ]
            
            for pattern in patterns:
                match = re.search(pattern, filename)
                if match:
                    return match.group(1)
                    
            return None
        except Exception as e:
            self.logger.error(f"提取金额失败 {filename}: {e}")
            return None
    
    def extract_single_pdf(self, pdf_path: str) -> PDFFile:
        """提取单个PDF文件内容"""
        pdf_file = PDFFile(path=pdf_path, filename=os.path.basename(pdf_path))
        
        try:
            # 提取金额
            pdf_file.amount = self.extract_amount_from_filename(pdf_file.filename)
            
            # 提取PDF内容
            reader = PdfReader(pdf_path)
            content_text = []
            
            for page in reader.pages:
                text = page.extract_text()
                if text.strip():
                    content_text.append(text.strip())
            
            if content_text:
                pdf_file.content = '\n'.join(content_text)
            else:
                pdf_file.error = "PDF内容为空"
                
        except Exception as e:
            pdf_file.error = str(e)
            self.logger.error(f"提取PDF失败 {pdf_file.filename}: {e}")
            
        return pdf_file
    
    def extract_pdf_content_parallel(self, folder_path: str, 
                                   pattern: str = "*_行程单.pdf") -> Dict[str, PDFFile]:
        """并发提取PDF内容"""
        folder_path = Path(folder_path)
        pdf_files = sorted(folder_path.glob(pattern))
        
        if not pdf_files:
            self.logger.warning(f"未找到匹配的PDF文件: {pattern}")
            return {}
        
        self.logger.info(f"找到 {len(pdf_files)} 个PDF文件")
        
        results = {}
        
        if self.config.enable_parallel and len(pdf_files) > 1:
            # 并发处理
            with ThreadPoolExecutor(max_workers=self.config.max_workers) as executor:
                # 提交任务
                future_to_path = {
                    executor.submit(self.extract_single_pdf, str(pdf_path)): pdf_path 
                    for pdf_path in pdf_files
                }
                
                # 收集结果
                for i, future in enumerate(as_completed(future_to_path), 1):
                    pdf_path = future_to_path[future]
                    try:
                        pdf_file = future.result()
                        results[pdf_file.filename] = pdf_file
                        
                        if pdf_file.error:
                            self.logger.error(f"处理失败 {i}/{len(pdf_files)}: {pdf_file.filename} - {pdf_file.error}")
                        else:
                            self.logger.info(f"处理完成 {i}/{len(pdf_files)}: {pdf_file.filename}")
                            
                    except Exception as e:
                        self.logger.error(f"处理异常 {pdf_path}: {e}")
        else:
            # 串行处理
            for i, pdf_path in enumerate(pdf_files, 1):
                self.logger.info(f"处理 {i}/{len(pdf_files)}: {pdf_path.name}")
                pdf_file = self.extract_single_pdf(str(pdf_path))
                results[pdf_file.filename] = pdf_file
                
                if pdf_file.error:
                    self.logger.error(f"  提取失败: {pdf_file.error}")
        
        successful_count = sum(1 for pdf in results.values() if pdf.content)
        self.logger.info(f"成功提取 {successful_count}/{len(results)} 个PDF文件的内容")
        
        return results
    
    def get_file_mappings(self, folder_path: str) -> Dict[str, Dict[str, str]]:
        """获取文件映射关系（行程单 <-> 发票）"""
        folder_path = Path(folder_path)
        itinerary_files = sorted(folder_path.glob("*_行程单.pdf"))
        
        mappings = {}
        
        for itinerary_file in itinerary_files:
            filename = itinerary_file.name
            amount = self.extract_amount_from_filename(filename)
            
            if amount:
                # 根据行程单文件名推导发票文件名
                invoice_filename = filename.replace("_行程单.pdf", ".pdf")
                invoice_file = folder_path / invoice_filename
                
                if invoice_file.exists():
                    mappings[amount] = {
                        'itinerary_original': str(itinerary_file),
                        'invoice_original': str(invoice_file)
                    }
                else:
                    self.logger.warning(f"未找到对应的发票文件: {invoice_filename}")
        
        return mappings
    
    def validate_pdf_file(self, pdf_path: str) -> Tuple[bool, str]:
        """验证PDF文件有效性"""
        try:
            pdf_path = Path(pdf_path)
            
            if not pdf_path.exists():
                return False, "文件不存在"
            
            if not pdf_path.is_file():
                return False, "不是文件"
            
            if pdf_path.suffix.lower() != '.pdf':
                return False, "不是PDF文件"
            
            if pdf_path.stat().st_size == 0:
                return False, "文件为空"
            
            # 尝试读取PDF
            reader = PdfReader(str(pdf_path))
            if len(reader.pages) == 0:
                return False, "PDF无页面"
            
            return True, "有效"
            
        except Exception as e:
            return False, f"验证失败: {str(e)}"
    
    def get_pdf_statistics(self, folder_path: str) -> Dict:
        """获取PDF文件统计信息"""
        folder_path = Path(folder_path)
        
        stats = {
            'total_files': 0,
            'itinerary_files': 0,
            'invoice_files': 0,
            'valid_files': 0,
            'invalid_files': 0,
            'total_size': 0,
            'errors': []
        }
        
        try:
            pdf_files = list(folder_path.glob("*.pdf"))
            stats['total_files'] = len(pdf_files)
            
            for pdf_file in pdf_files:
                # 统计文件类型
                if '_行程单.pdf' in pdf_file.name:
                    stats['itinerary_files'] += 1
                else:
                    stats['invoice_files'] += 1
                
                # 验证文件
                is_valid, message = self.validate_pdf_file(str(pdf_file))
                if is_valid:
                    stats['valid_files'] += 1
                    stats['total_size'] += pdf_file.stat().st_size
                else:
                    stats['invalid_files'] += 1
                    stats['errors'].append(f"{pdf_file.name}: {message}")
                    
        except Exception as e:
            self.logger.error(f"获取统计信息失败: {e}")
            stats['errors'].append(f"统计失败: {str(e)}")
        
        return stats
