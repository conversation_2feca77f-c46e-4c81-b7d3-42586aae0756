#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI管理器模块
负责用户界面交互和进度显示
"""

import os
import logging
from typing import Dict, List, Optional, Callable
from pathlib import Path
import threading
import time

from config import config_manager


class ProgressBar:
    """简单的进度条实现"""
    
    def __init__(self, total: int, description: str = "", width: int = 50):
        self.total = total
        self.current = 0
        self.description = description
        self.width = width
        self.start_time = time.time()
        self._lock = threading.Lock()
    
    def update(self, increment: int = 1, description: str = None):
        """更新进度"""
        with self._lock:
            self.current = min(self.current + increment, self.total)
            if description:
                self.description = description
            self._display()
    
    def _display(self):
        """显示进度条"""
        if self.total == 0:
            return
        
        percent = self.current / self.total
        filled_width = int(self.width * percent)
        bar = '█' * filled_width + '░' * (self.width - filled_width)
        
        elapsed_time = time.time() - self.start_time
        if self.current > 0:
            eta = elapsed_time * (self.total - self.current) / self.current
            eta_str = f"ETA: {eta:.1f}s"
        else:
            eta_str = "ETA: --"
        
        print(f"\r{self.description} [{bar}] {self.current}/{self.total} ({percent:.1%}) {eta_str}", end='', flush=True)
        
        if self.current >= self.total:
            print()  # 换行
    
    def finish(self, message: str = "完成"):
        """完成进度条"""
        with self._lock:
            self.current = self.total
            elapsed_time = time.time() - self.start_time
            print(f"\r{message} - 耗时: {elapsed_time:.1f}s")


class UIManager:
    """用户界面管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.config = config_manager.ui
    
    def show_banner(self):
        """显示程序横幅"""
        banner = """
╔══════════════════════════════════════════════════════════════╗
║                    PDF智能管理器 v2.0                        ║
║                  功能：提取 → 分析 → 重命名 → 归类              ║
╚══════════════════════════════════════════════════════════════╝
        """
        print(banner)
    
    def show_folder_selection(self, pdf_folders: List[Dict]) -> Optional[str]:
        """显示文件夹选择界面"""
        if not pdf_folders:
            print("❌ 未找到包含PDF文件的文件夹")
            return None

        if len(pdf_folders) == 1:
            folder = pdf_folders[0]
            print(f"📁 自动选择文件夹: {folder['name']} (包含 {folder['count']} 个PDF文件)")
            return folder['path']

        print("\n📂 发现多个包含PDF文件的文件夹：")
        print("─" * 60)
        for i, folder in enumerate(pdf_folders, 1):
            print(f"{i:2d}. 📁 {folder['name']} (包含 {folder['count']} 个PDF文件)")
        print("─" * 60)

        while True:
            try:
                choice = input("请选择文件夹序号: ").strip()
                if not choice:
                    return None
                
                index = int(choice) - 1
                if 0 <= index < len(pdf_folders):
                    selected = pdf_folders[index]
                    print(f"✅ 已选择: {selected['name']}")
                    return selected['path']
                else:
                    print("❌ 无效的序号，请重新输入")
            except ValueError:
                print("❌ 请输入有效的数字")
            except KeyboardInterrupt:
                print("\n👋 用户取消操作")
                return None
    
    def show_main_menu(self) -> str:
        """显示主菜单"""
        menu = """
╔══════════════════════════════════════════════════════════════╗
║                        PDF智能管理器                         ║
╠══════════════════════════════════════════════════════════════╣
║  1. 🚀 完整流程 (提取→分析→重命名→归类)                        ║
║  2. 📝 仅重命名 (适用于已提取内容的文件)                       ║
║  3. 📂 仅店铺归类 (适用于已重命名的文件)                       ║
║  4. 📊 查看文件状态                                           ║
║  5. ⚙️  配置管理                                              ║
║  0. 👋 退出                                                   ║
╚══════════════════════════════════════════════════════════════╝
        """
        print(menu)
        
        while True:
            try:
                choice = input("请选择功能 (0-5): ").strip()
                if choice in ['0', '1', '2', '3', '4', '5']:
                    return choice
                else:
                    print("❌ 无效选择，请输入 0-5")
            except KeyboardInterrupt:
                print("\n👋 用户取消操作")
                return '0'
    
    def show_file_status(self, status: Dict):
        """显示文件状态"""
        print("\n📊 文件状态报告:")
        print("─" * 40)
        print(f"📄 原始文件: {status['original_count']} 个")
        print(f"✏️  已重命名: {status['renamed_count']} 个")
        print(f"📁 店铺归类: {'✅ 已完成' if status['has_classification'] else '❌ 未完成'}")
        
        status_map = {
            'original': '🔵 原始状态',
            'renamed': '🟡 已重命名',
            'empty': '⚪ 无文件'
        }
        print(f"🔄 当前状态: {status_map.get(status['status'], '❓ 未知')}")
        
        if status.get('classification_folder'):
            print(f"📂 归类文件夹: {status['classification_folder']}")
    
    def show_statistics(self, stats: Dict):
        """显示统计信息"""
        print("\n📈 处理统计:")
        print("─" * 40)
        
        if 'total_files' in stats:
            print(f"📄 总文件数: {stats['total_files']}")
            print(f"📋 行程单: {stats.get('itinerary_files', 0)}")
            print(f"🧾 发票: {stats.get('invoice_files', 0)}")
            print(f"✅ 有效文件: {stats.get('valid_files', 0)}")
            print(f"❌ 无效文件: {stats.get('invalid_files', 0)}")
            
            if stats.get('total_size'):
                size_mb = stats['total_size'] / (1024 * 1024)
                print(f"💾 总大小: {size_mb:.1f} MB")
        
        if 'successful' in stats:
            print(f"✅ 成功分析: {stats['successful']}")
            print(f"❌ 分析失败: {stats['failed']}")
            print(f"📊 成功率: {stats.get('success_rate', 0):.1%}")
            print(f"🎯 平均置信度: {stats.get('average_confidence', 0):.2f}")
            print(f"🔄 平均重试次数: {stats.get('average_retries', 0):.1f}")
    
    def confirm_operation(self, message: str, default: bool = True) -> bool:
        """确认操作"""
        suffix = "(回车=确认)" if default else "(回车=取消)"
        prompt = f"\n❓ {message} {suffix}: "
        
        try:
            response = input(prompt).strip().lower()
            if not response:
                return default
            return response in ['y', 'yes', '是', '确认', '1', 'true']
        except KeyboardInterrupt:
            print("\n👋 用户取消操作")
            return False
    
    def show_errors(self, errors: List[str]):
        """显示错误信息"""
        if not errors:
            return
        
        print("\n⚠️  处理过程中出现的错误:")
        print("─" * 50)
        for i, error in enumerate(errors, 1):
            print(f"{i:2d}. ❌ {error}")
    
    def show_config_menu(self) -> str:
        """显示配置菜单"""
        menu = """
╔══════════════════════════════════════════════════════════════╗
║                        配置管理                              ║
╠══════════════════════════════════════════════════════════════╣
║  1. 🔑 API配置 (URL、密钥、模型)                              ║
║  2. ⚡ 性能配置 (并发数、批处理)                               ║
║  3. 🎨 界面配置 (语言、主题)                                   ║
║  4. 📝 提示词管理                                             ║
║  5. 💾 保存配置                                               ║
║  6. 🔄 重置为默认配置                                         ║
║  0. 🔙 返回主菜单                                             ║
╚══════════════════════════════════════════════════════════════╝
        """
        print(menu)
        
        while True:
            try:
                choice = input("请选择配置项 (0-6): ").strip()
                if choice in ['0', '1', '2', '3', '4', '5', '6']:
                    return choice
                else:
                    print("❌ 无效选择，请输入 0-6")
            except KeyboardInterrupt:
                return '0'
    
    def input_with_default(self, prompt: str, default: str = "") -> str:
        """带默认值的输入"""
        if default:
            full_prompt = f"{prompt} (默认: {default}): "
        else:
            full_prompt = f"{prompt}: "
        
        try:
            value = input(full_prompt).strip()
            return value if value else default
        except KeyboardInterrupt:
            return default
    
    def show_api_config(self, api_config):
        """显示API配置界面"""
        print("\n🔑 API配置:")
        print("─" * 40)
        print(f"🌐 API URL: {api_config.url}")
        print(f"🔐 API密钥: {'*' * 20}...{api_config.key[-8:] if len(api_config.key) > 8 else '未设置'}")
        print(f"🤖 模型: {api_config.model}")
        print(f"⏱️  超时时间: {api_config.timeout}秒")
        print(f"🔄 最大重试: {api_config.max_retries}次")
        print(f"⏳ 重试延迟: {api_config.retry_delay}秒")
    
    def show_processing_config(self, proc_config):
        """显示处理配置界面"""
        print("\n⚡ 性能配置:")
        print("─" * 40)
        print(f"👥 最大并发数: {proc_config.max_workers}")
        print(f"📦 批处理大小: {proc_config.batch_size}")
        print(f"🚀 启用并行处理: {'✅ 是' if proc_config.enable_parallel else '❌ 否'}")
        print(f"💾 启用备份: {'✅ 是' if proc_config.backup_enabled else '❌ 否'}")
    
    def create_progress_callback(self, description: str, total: int) -> Callable:
        """创建进度回调函数"""
        if not self.config.show_progress:
            return lambda *args, **kwargs: None
        
        progress_bar = ProgressBar(total, description)
        
        def callback(increment: int = 1, desc: str = None):
            progress_bar.update(increment, desc)
        
        return callback
    
    def wait_for_enter(self, message: str = "按回车键继续..."):
        """等待用户按回车"""
        try:
            input(f"\n{message}")
        except KeyboardInterrupt:
            pass
