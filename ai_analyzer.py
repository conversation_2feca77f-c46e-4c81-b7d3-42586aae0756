#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI分析器模块
负责调用AI API分析PDF内容，包含重试机制和错误处理
"""

import time
import json
import logging
import requests
from typing import Dict, Optional, List, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass
import threading
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from config import config_manager, prompt_manager
from pdf_extractor import PDFFile


@dataclass
class AnalysisResult:
    """分析结果"""
    filename: str
    amount: str
    destination: Optional[str] = None
    confidence: float = 0.0
    error: Optional[str] = None
    retry_count: int = 0


class AIAnalyzer:
    """AI分析器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.config = config_manager.api
        self.processing_config = config_manager.processing
        self._lock = threading.Lock()
        
        # 配置HTTP会话
        self.session = requests.Session()
        self._setup_session()
    
    def _setup_session(self):
        """配置HTTP会话，包含重试策略"""
        retry_strategy = Retry(
            total=self.config.max_retries,
            backoff_factor=self.config.retry_delay,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["POST"]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        # 设置默认超时
        self.session.timeout = self.config.timeout
    
    def _validate_api_response(self, response: requests.Response) -> Tuple[bool, str]:
        """验证API响应"""
        try:
            if response.status_code != 200:
                return False, f"HTTP错误: {response.status_code} - {response.text[:200]}"
            
            # 检查是否返回HTML（通常表示认证失败或URL错误）
            content_type = response.headers.get('content-type', '').lower()
            if 'text/html' in content_type:
                return False, "API返回HTML页面，可能是认证失败或URL错误"
            
            # 尝试解析JSON
            try:
                data = response.json()
            except json.JSONDecodeError as e:
                return False, f"JSON解析失败: {e} - 响应内容: {response.text[:200]}"
            
            # 检查响应结构
            if 'choices' not in data:
                return False, f"响应格式错误，缺少choices字段: {data}"
            
            if not data['choices'] or 'message' not in data['choices'][0]:
                return False, f"响应格式错误，choices为空或缺少message: {data}"
            
            return True, "验证通过"
            
        except Exception as e:
            return False, f"响应验证异常: {str(e)}"
    
    def call_llm_api(self, content: str, retry_count: int = 0) -> Tuple[Optional[str], int]:
        """调用大模型API分析内容"""
        prompt_template = prompt_manager.get_prompt("destination_extraction")
        prompt = prompt_template.format(content=content)
        
        headers = {
            'Authorization': f'Bearer {self.config.key}',
            'Content-Type': 'application/json'
        }
        
        data = {
            'model': self.config.model,
            'messages': [{'role': 'user', 'content': prompt}],
            'temperature': 0.1,
            'max_tokens': 200
        }
        
        for attempt in range(self.config.max_retries + 1):
            try:
                self.logger.debug(f"API调用尝试 {attempt + 1}/{self.config.max_retries + 1}")
                
                response = self.session.post(
                    self.config.url, 
                    headers=headers, 
                    json=data,
                    timeout=self.config.timeout
                )
                
                # 验证响应
                is_valid, message = self._validate_api_response(response)
                if not is_valid:
                    self.logger.warning(f"API响应验证失败: {message}")
                    if attempt < self.config.max_retries:
                        time.sleep(self.config.retry_delay * (2 ** attempt))  # 指数退避
                        continue
                    else:
                        return None, attempt + 1
                
                # 解析成功响应
                result = response.json()
                content = result['choices'][0]['message']['content'].strip()
                
                if content:
                    self.logger.debug(f"API调用成功，尝试次数: {attempt + 1}")
                    return content, attempt + 1
                else:
                    self.logger.warning("API返回空内容")
                    if attempt < self.config.max_retries:
                        time.sleep(self.config.retry_delay * (2 ** attempt))
                        continue
                    else:
                        return None, attempt + 1
                
            except requests.exceptions.Timeout:
                self.logger.warning(f"API调用超时 (尝试 {attempt + 1})")
                if attempt < self.config.max_retries:
                    time.sleep(self.config.retry_delay * (2 ** attempt))
                    continue
                else:
                    return None, attempt + 1
                    
            except requests.exceptions.ConnectionError as e:
                self.logger.warning(f"API连接错误 (尝试 {attempt + 1}): {e}")
                if attempt < self.config.max_retries:
                    time.sleep(self.config.retry_delay * (2 ** attempt))
                    continue
                else:
                    return None, attempt + 1
                    
            except Exception as e:
                self.logger.error(f"API调用异常 (尝试 {attempt + 1}): {e}")
                if attempt < self.config.max_retries:
                    time.sleep(self.config.retry_delay * (2 ** attempt))
                    continue
                else:
                    return None, attempt + 1
        
        return None, self.config.max_retries + 1
    
    def analyze_single_pdf(self, pdf_file: PDFFile) -> AnalysisResult:
        """分析单个PDF文件"""
        result = AnalysisResult(
            filename=pdf_file.filename,
            amount=pdf_file.amount or "unknown"
        )
        
        if not pdf_file.content:
            result.error = pdf_file.error or "PDF内容为空"
            return result
        
        if not pdf_file.amount:
            result.error = "无法从文件名提取金额"
            return result
        
        try:
            destination, retry_count = self.call_llm_api(pdf_file.content)
            result.retry_count = retry_count
            
            if destination:
                result.destination = destination
                result.confidence = self._calculate_confidence(destination, retry_count)
            else:
                result.error = f"AI分析失败，重试{retry_count}次"
                
        except Exception as e:
            result.error = f"分析异常: {str(e)}"
            self.logger.error(f"分析PDF异常 {pdf_file.filename}: {e}")
        
        return result
    
    def _calculate_confidence(self, destination: str, retry_count: int) -> float:
        """计算置信度"""
        base_confidence = 0.9
        
        # 根据重试次数降低置信度
        retry_penalty = (retry_count - 1) * 0.1
        
        # 根据内容质量调整置信度
        if not destination or len(destination.strip()) < 3:
            base_confidence *= 0.5
        elif '|' in destination:  # 包含详细信息
            base_confidence *= 1.1
        
        return max(0.0, min(1.0, base_confidence - retry_penalty))
    
    def analyze_destinations_parallel(self, pdf_files: Dict[str, PDFFile]) -> Dict[str, AnalysisResult]:
        """并发分析所有PDF文件的终点信息"""
        if not pdf_files:
            self.logger.warning("没有PDF文件可分析")
            return {}
        
        valid_files = {k: v for k, v in pdf_files.items() if v.content and v.amount}
        if not valid_files:
            self.logger.warning("没有有效的PDF文件可分析")
            return {}
        
        self.logger.info(f"开始分析 {len(valid_files)} 个PDF文件")
        results = {}
        
        if self.processing_config.enable_parallel and len(valid_files) > 1:
            # 并发分析
            with ThreadPoolExecutor(max_workers=self.processing_config.max_workers) as executor:
                future_to_filename = {
                    executor.submit(self.analyze_single_pdf, pdf_file): filename
                    for filename, pdf_file in valid_files.items()
                }
                
                for i, future in enumerate(as_completed(future_to_filename), 1):
                    filename = future_to_filename[future]
                    try:
                        result = future.result()
                        results[result.amount] = result
                        
                        if result.error:
                            self.logger.error(f"分析失败 {i}/{len(valid_files)}: {filename} - {result.error}")
                        else:
                            self.logger.info(f"分析完成 {i}/{len(valid_files)}: {filename} -> {result.destination}")
                            
                    except Exception as e:
                        self.logger.error(f"分析异常 {filename}: {e}")
                        
                    # 添加延迟避免API限流
                    if i < len(valid_files):
                        time.sleep(0.5)
        else:
            # 串行分析
            for i, (filename, pdf_file) in enumerate(valid_files.items(), 1):
                self.logger.info(f"分析 {i}/{len(valid_files)}: {filename}")
                
                result = self.analyze_single_pdf(pdf_file)
                results[result.amount] = result
                
                if result.error:
                    self.logger.error(f"  分析失败: {result.error}")
                else:
                    self.logger.info(f"  终点: {result.destination}")
                
                # 避免API限流
                if i < len(valid_files):
                    time.sleep(1)
        
        successful_count = sum(1 for r in results.values() if r.destination)
        self.logger.info(f"成功分析 {successful_count}/{len(results)} 个文件的终点信息")
        
        return results
    
    def get_analysis_statistics(self, results: Dict[str, AnalysisResult]) -> Dict:
        """获取分析统计信息"""
        if not results:
            return {}
        
        total = len(results)
        successful = sum(1 for r in results.values() if r.destination)
        failed = total - successful
        
        avg_confidence = sum(r.confidence for r in results.values() if r.destination) / max(successful, 1)
        avg_retries = sum(r.retry_count for r in results.values()) / total
        
        return {
            'total_files': total,
            'successful': successful,
            'failed': failed,
            'success_rate': successful / total if total > 0 else 0,
            'average_confidence': avg_confidence,
            'average_retries': avg_retries,
            'errors': [r.error for r in results.values() if r.error]
        }
