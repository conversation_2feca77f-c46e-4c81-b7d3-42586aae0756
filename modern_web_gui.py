#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF智能管理器 - 现代Web风格GUI
类似VS Code、Figma等现代应用的界面设计
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import queue
import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from config import config_manager
from pdf_extractor import PDFExtractor
from ai_analyzer import AIAnalyzer
from file_manager import FileManager


class ModernWebTheme:
    """现代Web风格主题"""
    
    # 深色主题配色 (类似VS Code Dark)
    COLORS = {
        # 背景色
        'bg_primary': '#1E1E1E',      # 主背景
        'bg_secondary': '#2D2D2D',    # 次要背景
        'bg_tertiary': '#3E3E3E',     # 第三背景
        'surface': '#252526',         # 表面色
        'surface_hover': '#2A2D2E',   # 表面悬停色
        
        # 主色调 (基于设计指南)
        'primary': '#3B82F6',         # 蓝色主色调
        'primary_hover': '#2563EB',   # 主色调悬停
        'primary_light': '#60A5FA',   # 浅蓝色
        
        # 语义色
        'success': '#10B981',         # 成功绿
        'warning': '#F59E0B',         # 警告橙
        'error': '#EF4444',           # 错误红
        'info': '#3B82F6',            # 信息蓝
        
        # 文字色
        'text_primary': '#FFFFFF',    # 主文字
        'text_secondary': '#CCCCCC',  # 次要文字
        'text_muted': '#969696',      # 弱化文字
        'text_disabled': '#6A6A6A',   # 禁用文字
        
        # 边框和分割线
        'border': '#404040',          # 边框色
        'border_light': '#505050',    # 浅边框色
        'divider': '#2D2D2D',         # 分割线
        
        # 状态色
        'accent': '#007ACC',          # 强调色
        'selection': '#264F78',       # 选中色
    }
    
    # 现代字体系统
    FONTS = {
        'title': ('Segoe UI', 18, 'bold'),
        'heading': ('Segoe UI', 14, 'bold'),
        'subheading': ('Segoe UI', 12, 'bold'),
        'body': ('Segoe UI', 11),
        'caption': ('Segoe UI', 10),
        'button': ('Segoe UI', 11, 'bold'),
        'code': ('Consolas', 10),
    }
    
    # 间距系统 (基于8px网格)
    SPACING = {
        'xs': 4, 'sm': 8, 'md': 16, 'lg': 24, 'xl': 32, 'xxl': 48
    }
    
    # 圆角系统
    RADIUS = {
        'sm': 4, 'md': 6, 'lg': 8, 'xl': 12
    }


class ModernCard(tk.Frame):
    """现代化卡片组件"""
    
    def __init__(self, parent, title="", **kwargs):
        super().__init__(
            parent,
            bg=ModernWebTheme.COLORS['surface'],
            relief='flat',
            bd=0,
            **kwargs
        )
        
        if title:
            self.setup_header(title)
        
        # 内容区域
        self.content = tk.Frame(
            self,
            bg=ModernWebTheme.COLORS['surface'],
            padx=ModernWebTheme.SPACING['md'],
            pady=ModernWebTheme.SPACING['md']
        )
        self.content.pack(fill=tk.BOTH, expand=True)
    
    def setup_header(self, title):
        """设置卡片头部"""
        header = tk.Frame(
            self,
            bg=ModernWebTheme.COLORS['surface'],
            height=40
        )
        header.pack(fill=tk.X)
        header.pack_propagate(False)
        
        title_label = tk.Label(
            header,
            text=title,
            font=ModernWebTheme.FONTS['subheading'],
            fg=ModernWebTheme.COLORS['text_primary'],
            bg=ModernWebTheme.COLORS['surface']
        )
        title_label.pack(side=tk.LEFT, padx=ModernWebTheme.SPACING['md'], pady=ModernWebTheme.SPACING['sm'])


class ModernButton(tk.Button):
    """现代化按钮组件"""
    
    def __init__(self, parent, text="", variant='primary', icon="", **kwargs):
        self.variant = variant
        self.icon = icon
        
        # 根据变体设置样式
        styles = self._get_button_styles()
        
        display_text = f"{icon} {text}" if icon else text
        
        super().__init__(
            parent,
            text=display_text,
            font=ModernWebTheme.FONTS['button'],
            bg=styles['bg'],
            fg=styles['fg'],
            activebackground=styles['hover_bg'],
            activeforeground=styles['fg'],
            relief='flat',
            bd=0,
            cursor='hand2',
            padx=ModernWebTheme.SPACING['md'],
            pady=ModernWebTheme.SPACING['sm'],
            **kwargs
        )
        
        # 绑定悬停效果
        self.bind('<Enter>', lambda e: self.configure(bg=styles['hover_bg']))
        self.bind('<Leave>', lambda e: self.configure(bg=styles['bg']))
    
    def _get_button_styles(self):
        """获取按钮样式"""
        styles = {
            'primary': {
                'bg': ModernWebTheme.COLORS['primary'],
                'fg': '#FFFFFF',
                'hover_bg': ModernWebTheme.COLORS['primary_hover']
            },
            'secondary': {
                'bg': ModernWebTheme.COLORS['bg_tertiary'],
                'fg': ModernWebTheme.COLORS['text_primary'],
                'hover_bg': ModernWebTheme.COLORS['surface_hover']
            },
            'success': {
                'bg': ModernWebTheme.COLORS['success'],
                'fg': '#FFFFFF',
                'hover_bg': '#059669'
            },
            'ghost': {
                'bg': 'transparent',
                'fg': ModernWebTheme.COLORS['text_secondary'],
                'hover_bg': ModernWebTheme.COLORS['surface_hover']
            }
        }
        return styles.get(self.variant, styles['primary'])


class SidebarNav(tk.Frame):
    """侧边栏导航组件"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(
            parent,
            bg=ModernWebTheme.COLORS['bg_secondary'],
            width=240,
            **kwargs
        )
        self.pack_propagate(False)
        
        self.current_page = "files"
        self.setup_nav()
    
    def setup_nav(self):
        """设置导航"""
        # 导航标题
        title_frame = tk.Frame(
            self,
            bg=ModernWebTheme.COLORS['bg_secondary'],
            height=60
        )
        title_frame.pack(fill=tk.X)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(
            title_frame,
            text="PDF智能管理器",
            font=ModernWebTheme.FONTS['heading'],
            fg=ModernWebTheme.COLORS['text_primary'],
            bg=ModernWebTheme.COLORS['bg_secondary']
        )
        title_label.pack(pady=ModernWebTheme.SPACING['md'])
        
        # 导航项目
        nav_items = [
            ("📁", "文件管理", "files"),
            ("🚀", "处理任务", "process"),
            ("📊", "统计报告", "stats"),
            ("⚙️", "系统设置", "settings")
        ]
        
        self.nav_buttons = {}
        for icon, text, page_id in nav_items:
            btn = self.create_nav_item(icon, text, page_id)
            self.nav_buttons[page_id] = btn
        
        # 设置默认选中
        self.set_active_page("files")
    
    def create_nav_item(self, icon, text, page_id):
        """创建导航项"""
        btn_frame = tk.Frame(
            self,
            bg=ModernWebTheme.COLORS['bg_secondary'],
            height=40
        )
        btn_frame.pack(fill=tk.X, padx=ModernWebTheme.SPACING['sm'], pady=2)
        btn_frame.pack_propagate(False)
        
        btn = tk.Button(
            btn_frame,
            text=f"{icon}  {text}",
            font=ModernWebTheme.FONTS['body'],
            bg=ModernWebTheme.COLORS['bg_secondary'],
            fg=ModernWebTheme.COLORS['text_secondary'],
            activebackground=ModernWebTheme.COLORS['selection'],
            activeforeground=ModernWebTheme.COLORS['text_primary'],
            relief='flat',
            bd=0,
            cursor='hand2',
            anchor='w',
            padx=ModernWebTheme.SPACING['md'],
            command=lambda: self.set_active_page(page_id)
        )
        btn.pack(fill=tk.BOTH, expand=True)
        
        # 悬停效果
        def on_enter(e):
            if page_id != self.current_page:
                btn.configure(bg=ModernWebTheme.COLORS['surface_hover'])
        
        def on_leave(e):
            if page_id != self.current_page:
                btn.configure(bg=ModernWebTheme.COLORS['bg_secondary'])
        
        btn.bind('<Enter>', on_enter)
        btn.bind('<Leave>', on_leave)
        
        return btn
    
    def set_active_page(self, page_id):
        """设置活动页面"""
        # 重置所有按钮
        for pid, btn in self.nav_buttons.items():
            if pid == page_id:
                btn.configure(
                    bg=ModernWebTheme.COLORS['selection'],
                    fg=ModernWebTheme.COLORS['text_primary']
                )
            else:
                btn.configure(
                    bg=ModernWebTheme.COLORS['bg_secondary'],
                    fg=ModernWebTheme.COLORS['text_secondary']
                )
        
        self.current_page = page_id
        
        # 触发页面切换事件
        if hasattr(self.master, 'switch_page'):
            self.master.switch_page(page_id)


class TopBar(tk.Frame):
    """顶部栏组件"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(
            parent,
            bg=ModernWebTheme.COLORS['bg_primary'],
            height=50,
            **kwargs
        )
        self.pack_propagate(False)
        self.setup_topbar()
    
    def setup_topbar(self):
        """设置顶部栏"""
        # 左侧：当前页面标题
        self.page_title = tk.Label(
            self,
            text="文件管理",
            font=ModernWebTheme.FONTS['title'],
            fg=ModernWebTheme.COLORS['text_primary'],
            bg=ModernWebTheme.COLORS['bg_primary']
        )
        self.page_title.pack(side=tk.LEFT, padx=ModernWebTheme.SPACING['lg'], pady=ModernWebTheme.SPACING['sm'])
        
        # 右侧：操作按钮
        right_frame = tk.Frame(
            self,
            bg=ModernWebTheme.COLORS['bg_primary']
        )
        right_frame.pack(side=tk.RIGHT, padx=ModernWebTheme.SPACING['lg'], pady=ModernWebTheme.SPACING['sm'])
        
        # 主题切换按钮
        theme_btn = ModernButton(
            right_frame,
            text="🌙",
            variant='ghost',
            command=self.toggle_theme
        )
        theme_btn.pack(side=tk.RIGHT, padx=(ModernWebTheme.SPACING['sm'], 0))
        
        # 设置按钮
        settings_btn = ModernButton(
            right_frame,
            text="⚙️",
            variant='ghost',
            command=self.show_settings
        )
        settings_btn.pack(side=tk.RIGHT)
    
    def update_title(self, title):
        """更新页面标题"""
        self.page_title.configure(text=title)
    
    def toggle_theme(self):
        """切换主题"""
        messagebox.showinfo("主题切换", "主题切换功能开发中...")
    
    def show_settings(self):
        """显示设置"""
        if hasattr(self.master, 'show_settings'):
            self.master.show_settings()


class ModernWebPDFManagerGUI:
    """现代Web风格PDF管理器GUI"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.setup_layout()
        
        # 业务逻辑组件
        self.pdf_extractor = PDFExtractor()
        self.ai_analyzer = AIAnalyzer()
        self.file_manager = FileManager()
        
        # 状态变量
        self.current_folder = None
        self.processing = False
        
        # 消息队列
        self.message_queue = queue.Queue()
        self.check_queue()
    
    def setup_window(self):
        """设置主窗口"""
        self.root.title("PDF智能管理器")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 600)
        self.root.configure(bg=ModernWebTheme.COLORS['bg_primary'])
        
        # 居中显示
        self.center_window()
    
    def center_window(self):
        """窗口居中"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def setup_layout(self):
        """设置布局"""
        # 顶部栏
        self.topbar = TopBar(self.root)
        self.topbar.pack(fill=tk.X, side=tk.TOP)
        
        # 主内容区域
        main_container = tk.Frame(
            self.root,
            bg=ModernWebTheme.COLORS['bg_primary']
        )
        main_container.pack(fill=tk.BOTH, expand=True)
        
        # 侧边栏
        self.sidebar = SidebarNav(main_container)
        self.sidebar.pack(side=tk.LEFT, fill=tk.Y)
        
        # 内容区域
        self.content_area = tk.Frame(
            main_container,
            bg=ModernWebTheme.COLORS['bg_primary'],
            padx=ModernWebTheme.SPACING['lg'],
            pady=ModernWebTheme.SPACING['lg']
        )
        self.content_area.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 初始化页面
        self.setup_pages()
        self.switch_page("files")
    
    def setup_pages(self):
        """设置页面"""
        self.pages = {}
        
        # 文件管理页面
        self.pages["files"] = self.create_files_page()
        
        # 处理任务页面
        self.pages["process"] = self.create_process_page()
        
        # 统计报告页面
        self.pages["stats"] = self.create_stats_page()
        
        # 系统设置页面
        self.pages["settings"] = self.create_settings_page()
    
    def create_files_page(self):
        """创建文件管理页面"""
        page = tk.Frame(self.content_area, bg=ModernWebTheme.COLORS['bg_primary'])
        
        # 文件选择卡片
        file_card = ModernCard(page, "文件夹选择")
        file_card.pack(fill=tk.X, pady=(0, ModernWebTheme.SPACING['lg']))
        
        # 选择按钮
        select_btn = ModernButton(
            file_card.content,
            text="选择文件夹",
            icon="📁",
            variant='primary',
            command=self.select_folder
        )
        select_btn.pack(anchor='w')
        
        # 文件夹信息
        self.folder_info = tk.Label(
            file_card.content,
            text="请选择包含PDF文件的文件夹",
            font=ModernWebTheme.FONTS['body'],
            fg=ModernWebTheme.COLORS['text_muted'],
            bg=ModernWebTheme.COLORS['surface'],
            wraplength=500,
            justify=tk.LEFT
        )
        self.folder_info.pack(anchor='w', pady=(ModernWebTheme.SPACING['sm'], 0))
        
        # 文件统计卡片
        stats_card = ModernCard(page, "文件统计")
        stats_card.pack(fill=tk.X, pady=(0, ModernWebTheme.SPACING['lg']))
        
        # 统计网格
        stats_grid = tk.Frame(stats_card.content, bg=ModernWebTheme.COLORS['surface'])
        stats_grid.pack(fill=tk.X)
        
        # 统计项目
        self.stats_labels = {}
        stats_items = [
            ("total", "总文件数", "📄"),
            ("itinerary", "行程单", "🎫"),
            ("invoice", "发票", "🧾"),
            ("valid", "有效文件", "✅")
        ]
        
        for i, (key, label, icon) in enumerate(stats_items):
            col = i % 2
            row = i // 2
            
            stat_frame = tk.Frame(stats_grid, bg=ModernWebTheme.COLORS['surface'])
            stat_frame.grid(row=row, column=col, padx=ModernWebTheme.SPACING['sm'], 
                           pady=ModernWebTheme.SPACING['sm'], sticky='w')
            
            icon_label = tk.Label(
                stat_frame,
                text=icon,
                font=('Segoe UI Emoji', 16),
                bg=ModernWebTheme.COLORS['surface']
            )
            icon_label.pack(side=tk.LEFT, padx=(0, ModernWebTheme.SPACING['sm']))
            
            text_frame = tk.Frame(stat_frame, bg=ModernWebTheme.COLORS['surface'])
            text_frame.pack(side=tk.LEFT)
            
            value_label = tk.Label(
                text_frame,
                text="0",
                font=ModernWebTheme.FONTS['heading'],
                fg=ModernWebTheme.COLORS['text_primary'],
                bg=ModernWebTheme.COLORS['surface']
            )
            value_label.pack(anchor='w')
            
            desc_label = tk.Label(
                text_frame,
                text=label,
                font=ModernWebTheme.FONTS['caption'],
                fg=ModernWebTheme.COLORS['text_muted'],
                bg=ModernWebTheme.COLORS['surface']
            )
            desc_label.pack(anchor='w')
            
            self.stats_labels[key] = value_label
        
        return page

    def create_process_page(self):
        """创建处理任务页面"""
        page = tk.Frame(self.content_area, bg=ModernWebTheme.COLORS['bg_primary'])

        # 任务选择卡片
        task_card = ModernCard(page, "选择处理任务")
        task_card.pack(fill=tk.X, pady=(0, ModernWebTheme.SPACING['lg']))

        # 任务按钮网格
        task_grid = tk.Frame(task_card.content, bg=ModernWebTheme.COLORS['surface'])
        task_grid.pack(fill=tk.X)

        tasks = [
            ("🔄", "完整流程", "提取→分析→重命名→归类", self.full_process, 'primary'),
            ("📝", "仅重命名", "AI分析并重命名文件", self.rename_only, 'secondary'),
            ("📂", "仅归类", "按店铺归类已重命名文件", self.classify_only, 'secondary'),
            ("📊", "查看状态", "查看当前处理状态", self.show_status, 'ghost')
        ]

        self.task_buttons = []
        for i, (icon, title, desc, command, variant) in enumerate(tasks):
            col = i % 2
            row = i // 2

            task_frame = tk.Frame(task_grid, bg=ModernWebTheme.COLORS['surface'])
            task_frame.grid(row=row, column=col, padx=ModernWebTheme.SPACING['sm'],
                           pady=ModernWebTheme.SPACING['sm'], sticky='ew')

            btn = ModernButton(
                task_frame,
                text=f"{icon} {title}",
                variant=variant,
                command=command
            )
            btn.pack(fill=tk.X)

            desc_label = tk.Label(
                task_frame,
                text=desc,
                font=ModernWebTheme.FONTS['caption'],
                fg=ModernWebTheme.COLORS['text_muted'],
                bg=ModernWebTheme.COLORS['surface']
            )
            desc_label.pack(pady=(ModernWebTheme.SPACING['xs'], 0))

            self.task_buttons.append(btn)

        task_grid.columnconfigure(0, weight=1)
        task_grid.columnconfigure(1, weight=1)

        # 进度卡片
        progress_card = ModernCard(page, "处理进度")
        progress_card.pack(fill=tk.X, pady=(0, ModernWebTheme.SPACING['lg']))

        # 状态显示
        self.status_label = tk.Label(
            progress_card.content,
            text="就绪",
            font=ModernWebTheme.FONTS['body'],
            fg=ModernWebTheme.COLORS['text_primary'],
            bg=ModernWebTheme.COLORS['surface']
        )
        self.status_label.pack(anchor='w')

        # 进度条
        self.progress_bar = ttk.Progressbar(
            progress_card.content,
            mode='determinate',
            length=400
        )
        self.progress_bar.pack(fill=tk.X, pady=(ModernWebTheme.SPACING['sm'], 0))

        # 日志卡片
        log_card = ModernCard(page, "处理日志")
        log_card.pack(fill=tk.BOTH, expand=True)

        # 日志文本区域
        log_frame = tk.Frame(log_card.content, bg=ModernWebTheme.COLORS['surface'])
        log_frame.pack(fill=tk.BOTH, expand=True)

        self.log_text = tk.Text(
            log_frame,
            font=ModernWebTheme.FONTS['code'],
            bg=ModernWebTheme.COLORS['bg_tertiary'],
            fg=ModernWebTheme.COLORS['text_primary'],
            insertbackground=ModernWebTheme.COLORS['primary'],
            selectbackground=ModernWebTheme.COLORS['selection'],
            wrap=tk.WORD,
            state=tk.DISABLED,
            relief='flat',
            bd=0,
            padx=ModernWebTheme.SPACING['sm'],
            pady=ModernWebTheme.SPACING['sm']
        )

        scrollbar = tk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)

        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 初始禁用任务按钮
        self.enable_task_buttons(False)

        return page

    def create_stats_page(self):
        """创建统计报告页面"""
        page = tk.Frame(self.content_area, bg=ModernWebTheme.COLORS['bg_primary'])

        # 占位内容
        placeholder = tk.Label(
            page,
            text="📊 统计报告功能开发中...",
            font=ModernWebTheme.FONTS['heading'],
            fg=ModernWebTheme.COLORS['text_muted'],
            bg=ModernWebTheme.COLORS['bg_primary']
        )
        placeholder.pack(expand=True)

        return page

    def create_settings_page(self):
        """创建系统设置页面"""
        page = tk.Frame(self.content_area, bg=ModernWebTheme.COLORS['bg_primary'])

        # API配置卡片
        api_card = ModernCard(page, "API配置")
        api_card.pack(fill=tk.X, pady=(0, ModernWebTheme.SPACING['lg']))

        # API URL
        url_frame = tk.Frame(api_card.content, bg=ModernWebTheme.COLORS['surface'])
        url_frame.pack(fill=tk.X, pady=(0, ModernWebTheme.SPACING['md']))

        tk.Label(
            url_frame,
            text="API地址",
            font=ModernWebTheme.FONTS['body'],
            fg=ModernWebTheme.COLORS['text_primary'],
            bg=ModernWebTheme.COLORS['surface']
        ).pack(anchor='w')

        self.api_url_entry = tk.Entry(
            url_frame,
            font=ModernWebTheme.FONTS['body'],
            bg=ModernWebTheme.COLORS['bg_tertiary'],
            fg=ModernWebTheme.COLORS['text_primary'],
            insertbackground=ModernWebTheme.COLORS['primary'],
            relief='flat',
            bd=1,
            highlightthickness=1,
            highlightcolor=ModernWebTheme.COLORS['primary']
        )
        self.api_url_entry.pack(fill=tk.X, pady=(ModernWebTheme.SPACING['xs'], 0))
        self.api_url_entry.insert(0, config_manager.api.url)

        # API Key
        key_frame = tk.Frame(api_card.content, bg=ModernWebTheme.COLORS['surface'])
        key_frame.pack(fill=tk.X, pady=(0, ModernWebTheme.SPACING['md']))

        tk.Label(
            key_frame,
            text="API密钥",
            font=ModernWebTheme.FONTS['body'],
            fg=ModernWebTheme.COLORS['text_primary'],
            bg=ModernWebTheme.COLORS['surface']
        ).pack(anchor='w')

        self.api_key_entry = tk.Entry(
            key_frame,
            font=ModernWebTheme.FONTS['body'],
            bg=ModernWebTheme.COLORS['bg_tertiary'],
            fg=ModernWebTheme.COLORS['text_primary'],
            insertbackground=ModernWebTheme.COLORS['primary'],
            relief='flat',
            bd=1,
            highlightthickness=1,
            highlightcolor=ModernWebTheme.COLORS['primary'],
            show='*'
        )
        self.api_key_entry.pack(fill=tk.X, pady=(ModernWebTheme.SPACING['xs'], 0))
        self.api_key_entry.insert(0, config_manager.api.key)

        # 按钮区域
        btn_frame = tk.Frame(api_card.content, bg=ModernWebTheme.COLORS['surface'])
        btn_frame.pack(fill=tk.X, pady=(ModernWebTheme.SPACING['md'], 0))

        test_btn = ModernButton(
            btn_frame,
            text="🔗 测试连接",
            variant='secondary',
            command=self.test_api
        )
        test_btn.pack(side=tk.LEFT)

        save_btn = ModernButton(
            btn_frame,
            text="💾 保存配置",
            variant='primary',
            command=self.save_api_config
        )
        save_btn.pack(side=tk.RIGHT)

        return page

    def switch_page(self, page_id):
        """切换页面"""
        # 隐藏所有页面
        for page in self.pages.values():
            page.pack_forget()

        # 显示选中页面
        if page_id in self.pages:
            self.pages[page_id].pack(fill=tk.BOTH, expand=True)

        # 更新顶部栏标题
        titles = {
            "files": "文件管理",
            "process": "处理任务",
            "stats": "统计报告",
            "settings": "系统设置"
        }
        self.topbar.update_title(titles.get(page_id, "未知页面"))

    def select_folder(self):
        """选择文件夹"""
        folder = filedialog.askdirectory(title="选择包含PDF文件的文件夹")
        if folder:
            self.current_folder = folder
            folder_name = Path(folder).name
            self.folder_info.configure(
                text=f"已选择：{folder_name}\n路径：{folder}",
                fg=ModernWebTheme.COLORS['text_primary']
            )
            self.log_message(f"选择文件夹：{folder_name}")
            self.check_folder()

    def check_folder(self):
        """检查文件夹"""
        if not self.current_folder:
            return

        try:
            stats = self.pdf_extractor.get_pdf_statistics(self.current_folder)

            # 更新统计显示
            self.stats_labels['total'].configure(text=str(stats.get('total_files', 0)))
            self.stats_labels['itinerary'].configure(text=str(stats.get('itinerary_files', 0)))
            self.stats_labels['invoice'].configure(text=str(stats.get('invoice_files', 0)))
            self.stats_labels['valid'].configure(text=str(stats.get('valid_files', 0)))

            if stats.get('itinerary_files', 0) > 0:
                self.log_message(f"✅ 发现 {stats['total_files']} 个PDF文件")
                self.enable_task_buttons(True)
            else:
                self.log_message("⚠️ 未发现行程单格式的文件")
                self.enable_task_buttons(False)

        except Exception as e:
            self.log_message(f"❌ 检查失败：{str(e)}")
            self.enable_task_buttons(False)

    def enable_task_buttons(self, enabled):
        """启用/禁用任务按钮"""
        state = tk.NORMAL if enabled else tk.DISABLED
        for btn in self.task_buttons:
            btn.configure(state=state)

    def log_message(self, message):
        """添加日志"""
        import time
        timestamp = time.strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"

        self.log_text.configure(state=tk.NORMAL)
        self.log_text.insert(tk.END, formatted_message)
        self.log_text.see(tk.END)
        self.log_text.configure(state=tk.DISABLED)
        self.root.update()

    def update_progress(self, status, progress=None):
        """更新进度"""
        self.status_label.configure(text=status)
        if progress is not None:
            self.progress_bar['value'] = progress
        self.root.update()

    def test_api(self):
        """测试API连接"""
        url = self.api_url_entry.get()
        key = self.api_key_entry.get()

        if not url or not key:
            messagebox.showwarning("提示", "请填写API地址和密钥")
            return

        try:
            import requests
            headers = {
                'Authorization': f'Bearer {key}',
                'Content-Type': 'application/json'
            }
            data = {
                'model': config_manager.api.model,
                'messages': [{'role': 'user', 'content': '测试'}],
                'max_tokens': 5
            }

            response = requests.post(url, headers=headers, json=data, timeout=10)
            if response.status_code == 200:
                messagebox.showinfo("测试成功", "✅ API连接正常")
            else:
                messagebox.showerror("测试失败", f"❌ 连接失败：{response.status_code}")
        except Exception as e:
            messagebox.showerror("测试失败", f"❌ 连接错误：{str(e)}")

    def save_api_config(self):
        """保存API配置"""
        try:
            config_manager.update_api_config(
                url=self.api_url_entry.get(),
                key=self.api_key_entry.get()
            )
            if config_manager.save_config():
                messagebox.showinfo("保存成功", "✅ 配置已保存")
            else:
                messagebox.showerror("保存失败", "❌ 配置保存失败")
        except Exception as e:
            messagebox.showerror("保存失败", f"❌ 保存错误：{str(e)}")

    def show_settings(self):
        """显示设置页面"""
        self.sidebar.set_active_page("settings")

    # 处理任务方法
    def full_process(self):
        """完整流程"""
        if not self.current_folder:
            messagebox.showwarning("提示", "请先选择文件夹")
            return

        if messagebox.askyesno("确认", "执行完整流程？\n包括：提取→分析→重命名→归类"):
            self.start_processing("完整流程", self._full_process_worker)

    def rename_only(self):
        """仅重命名"""
        if not self.current_folder:
            messagebox.showwarning("提示", "请先选择文件夹")
            return

        if messagebox.askyesno("确认", "执行重命名操作？"):
            self.start_processing("重命名", self._rename_worker)

    def classify_only(self):
        """仅归类"""
        if not self.current_folder:
            messagebox.showwarning("提示", "请先选择文件夹")
            return

        if messagebox.askyesno("确认", "执行店铺归类？"):
            self.start_processing("店铺归类", self._classify_worker)

    def show_status(self):
        """显示状态"""
        if not self.current_folder:
            messagebox.showwarning("提示", "请先选择文件夹")
            return

        try:
            status = self.file_manager.get_file_status(self.current_folder)
            stats = self.pdf_extractor.get_pdf_statistics(self.current_folder)

            info = f"""📊 文件状态报告

📁 文件夹：{Path(self.current_folder).name}

📄 文件统计：
   • 总文件数：{stats['total_files']} 个
   • 行程单：{stats.get('itinerary_files', 0)} 个
   • 发票：{stats.get('invoice_files', 0)} 个
   • 有效文件：{stats.get('valid_files', 0)} 个

🔄 处理状态：
   • 原始文件：{status['original_count']} 个
   • 已重命名：{status['renamed_count']} 个
   • 店铺归类：{'✅ 已完成' if status['has_classification'] else '❌ 未完成'}"""

            messagebox.showinfo("文件状态", info)
        except Exception as e:
            messagebox.showerror("错误", f"获取状态失败：{str(e)}")

    def start_processing(self, task_name, worker_func):
        """开始处理"""
        if self.processing:
            messagebox.showwarning("提示", "正在处理中，请稍候")
            return

        self.processing = True
        self.enable_task_buttons(False)
        self.log_message(f"🚀 开始{task_name}")

        thread = threading.Thread(target=worker_func, daemon=True)
        thread.start()

    def _full_process_worker(self):
        """完整流程工作线程"""
        try:
            self.message_queue.put(("progress", "🔍 提取PDF内容", 20))
            pdf_files = self.pdf_extractor.extract_pdf_content_parallel(self.current_folder)

            if not pdf_files:
                self.message_queue.put(("error", "未找到PDF文件"))
                return

            self.message_queue.put(("progress", "🤖 AI分析中", 50))
            ai_results = self.ai_analyzer.analyze_destinations_parallel(pdf_files)

            if not ai_results:
                self.message_queue.put(("error", "AI分析失败"))
                return

            self.message_queue.put(("progress", "📝 重命名文件", 75))
            file_mappings = self.pdf_extractor.get_file_mappings(self.current_folder)
            success_count, errors = self.file_manager.execute_rename_with_lock(file_mappings, ai_results)

            self.message_queue.put(("progress", "📂 店铺归类", 90))
            shop_data = self.file_manager.scan_renamed_files(self.current_folder)
            self.file_manager.create_shop_classification(self.current_folder, shop_data)

            self.message_queue.put(("success", f"✅ 完成！处理了 {success_count} 个文件"))

        except Exception as e:
            self.message_queue.put(("error", f"❌ 处理失败：{str(e)}"))

    def _rename_worker(self):
        """重命名工作线程"""
        try:
            self.message_queue.put(("progress", "🔍 提取PDF内容", 30))
            pdf_files = self.pdf_extractor.extract_pdf_content_parallel(self.current_folder)

            self.message_queue.put(("progress", "🤖 AI分析中", 70))
            ai_results = self.ai_analyzer.analyze_destinations_parallel(pdf_files)

            self.message_queue.put(("progress", "📝 重命名文件", 90))
            file_mappings = self.pdf_extractor.get_file_mappings(self.current_folder)
            success_count, errors = self.file_manager.execute_rename_with_lock(file_mappings, ai_results)

            self.message_queue.put(("success", f"✅ 重命名完成！处理了 {success_count} 个文件"))

        except Exception as e:
            self.message_queue.put(("error", f"❌ 重命名失败：{str(e)}"))

    def _classify_worker(self):
        """归类工作线程"""
        try:
            self.message_queue.put(("progress", "📂 扫描文件", 50))
            shop_data = self.file_manager.scan_renamed_files(self.current_folder)

            self.message_queue.put(("progress", "🏪 店铺归类", 90))
            success, errors = self.file_manager.create_shop_classification(self.current_folder, shop_data)

            self.message_queue.put(("success", f"✅ 归类完成！整理了 {len(shop_data)} 个店铺"))

        except Exception as e:
            self.message_queue.put(("error", f"❌ 归类失败：{str(e)}"))

    def check_queue(self):
        """检查消息队列"""
        try:
            while True:
                msg_type, *args = self.message_queue.get_nowait()

                if msg_type == "progress":
                    status, progress = args
                    self.update_progress(status, progress)
                    self.log_message(status)

                elif msg_type == "success":
                    message = args[0]
                    self.processing = False
                    self.enable_task_buttons(True)
                    self.update_progress("✅ 完成", 100)
                    self.log_message(message)
                    messagebox.showinfo("处理完成", message)
                    self.check_folder()

                elif msg_type == "error":
                    message = args[0]
                    self.processing = False
                    self.enable_task_buttons(True)
                    self.update_progress("❌ 错误", 0)
                    self.log_message(message)
                    messagebox.showerror("处理失败", message)

        except queue.Empty:
            pass

        self.root.after(100, self.check_queue)

    def run(self):
        """运行应用"""
        self.log_message("🚀 PDF智能管理器已启动")
        self.log_message("📁 请在文件管理页面选择PDF文件夹")
        self.root.mainloop()


def main():
    """主函数"""
    try:
        app = ModernWebPDFManagerGUI()
        app.run()
    except Exception as e:
        messagebox.showerror("启动失败", f"❌ 启动失败：{str(e)}")


if __name__ == "__main__":
    main()
