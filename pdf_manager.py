#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF智能管理器 - 一键脚本
功能：PDF提取 → AI分析 → 智能重命名 → 店铺归类
"""

import os
import re
import glob
import json
import time
import shutil
import requests
from PyPDF2 import PdfReader
from collections import defaultdict

# API配置
API_URL = "https://llm.chutes.ai/v1/chat/completions"
API_KEY = "cpk_ad5240eadb98407eaf5c27bd28801284.4e2d3369b33d5c0ba2f14b1032b0daf8.l8C9MNW0SNMpnApQHd44iUy3SaWR019s"
MODEL = "deepseek-ai/DeepSeek-V3-0324"

class PDFManager:
    def __init__(self):
        self.current_folder = None
        self.pdf_content = {}
        self.ai_results = {}
        self.shop_data = defaultdict(list)
        
    def find_pdf_folders(self):
        """自动识别包含PDF文件的文件夹"""
        current_dir = os.getcwd()
        pdf_folders = []
        
        # 检查当前目录
        pdf_files = glob.glob(os.path.join(current_dir, "*.pdf"))
        if pdf_files:
            pdf_folders.append({
                'path': current_dir,
                'name': '当前目录',
                'count': len(pdf_files)
            })
        
        # 检查子目录
        for item in os.listdir(current_dir):
            item_path = os.path.join(current_dir, item)
            if os.path.isdir(item_path) and not item.startswith('.'):
                pdf_files = glob.glob(os.path.join(item_path, "*.pdf"))
                if pdf_files:
                    pdf_folders.append({
                        'path': item_path,
                        'name': item,
                        'count': len(pdf_files)
                    })
        
        return pdf_folders
    
    def select_folder(self, pdf_folders):
        """让用户选择PDF文件夹"""
        if not pdf_folders:
            print("未找到包含PDF文件的文件夹")
            return None

        if len(pdf_folders) == 1:
            folder = pdf_folders[0]
            print(f"自动选择文件夹: {folder['name']} (包含 {folder['count']} 个PDF文件)")
            return folder['path']

        print("发现多个包含PDF文件的文件夹：")
        print("-" * 60)
        for i, folder in enumerate(pdf_folders, 1):
            print(f"{i}. {folder['name']} (包含 {folder['count']} 个PDF文件)")
        print("-" * 60)

        while True:
            try:
                choice = input("请选择文件夹序号: ")
                index = int(choice) - 1
                if 0 <= index < len(pdf_folders):
                    selected = pdf_folders[index]
                    print(f"已选择: {selected['name']}")
                    return selected['path']
                else:
                    print("无效的序号，请重新输入")
            except ValueError:
                print("请输入有效的数字")
    
    def check_file_status(self):
        """检查文件状态"""
        original_pattern = os.path.join(self.current_folder, "行程单-*.pdf")
        renamed_pattern = os.path.join(self.current_folder, "*-行程单.pdf")
        classified_folder = os.path.join(self.current_folder, "按店铺归类")
        
        original_files = glob.glob(original_pattern)
        renamed_files = glob.glob(renamed_pattern)
        has_classification = os.path.exists(classified_folder)
        
        return {
            'original_count': len(original_files),
            'renamed_count': len(renamed_files),
            'has_classification': has_classification,
            'status': 'original' if original_files else ('renamed' if renamed_files else 'empty')
        }
    
    def extract_pdf_content(self):
        """提取PDF内容"""
        print("\n开始提取PDF内容...")

        pattern = os.path.join(self.current_folder, "行程单-*.pdf")
        pdf_files = sorted(glob.glob(pattern))

        if not pdf_files:
            print("未找到行程单PDF文件")
            return False

        print(f"找到 {len(pdf_files)} 个行程单PDF文件")

        content_data = {}
        for i, pdf_path in enumerate(pdf_files, 1):
            filename = os.path.basename(pdf_path)
            print(f"处理 {i}/{len(pdf_files)}: {filename}")

            try:
                reader = PdfReader(pdf_path)
                content_text = []
                for page in reader.pages:
                    text = page.extract_text()
                    if text.strip():
                        content_text.append(text.strip())

                if content_text:
                    content_data[filename] = '\n'.join(content_text)

            except Exception as e:
                print(f"  提取失败: {str(e)}")

        self.pdf_content = content_data
        print(f"成功提取 {len(content_data)} 个PDF文件的内容")
        return True
    
    def call_llm_api(self, content):
        """调用大模型API分析行程单内容"""
        prompt = f"""# Role: 行程终点提取器

## Profile
- language: 中文
- description: 专业分析用户提供的行程单内容，高效识别并原样输出最后一个行程的终点信息。
- personality: 严谨、保守、高效；拒绝任何改动或添加，坚持一丝不苟的输出方式。

## Rules
1. 基本原则：
   - 忠实原样输出：必须一字不差地复制终点信息，包括所有符号、地址、店铺信息或括号内容。
   - 最大序号选择：仅以行程单中序号最大的记录（即最后一个行程）为准。
   - 纯信息返回：输出只包含终点信息本身，拒绝附带解释、原因或上下文。

2. 行为准则：
   - 输入解析：解析内容，提取所有行程记录列表。
   - 终点定位：识别并选择列表中最高序号（最后一个）的记录。
   - 直接复制：原样复制终点信息部分，不进行任何处理。

3. 限制条件：
   - 禁止修改：终点信息不允许任何编辑、简化或优化。
   - 禁用解释：不输出额外文本，如提示、错误消息或过程描述。

## 任务
从以下行程单中精确提取最后一个行程的完整终点信息：

{content}

请直接返回完整的终点信息："""
        
        headers = {
            'Authorization': f'Bearer {API_KEY}',
            'Content-Type': 'application/json'
        }
        
        data = {
            'model': MODEL,
            'messages': [{'role': 'user', 'content': prompt}],
            'temperature': 0.1,
            'max_tokens': 200
        }
        
        try:
            response = requests.post(API_URL, headers=headers, json=data, timeout=60)
            response.raise_for_status()
            result = response.json()
            return result['choices'][0]['message']['content'].strip()
        except Exception as e:
            print(f"  API调用失败: {e}")
            return None
    
    def analyze_destinations(self):
        """使用AI分析所有行程单的终点信息"""
        print("\n开始AI分析终点信息...")

        if not self.pdf_content:
            print("没有PDF内容可分析")
            return False

        results = {}
        for i, (filename, content) in enumerate(self.pdf_content.items(), 1):
            print(f"分析 {i}/{len(self.pdf_content)}: {filename}")

            destination = self.call_llm_api(content)
            if destination:
                amount = self.extract_amount_from_filename(filename)
                if amount:
                    results[amount] = destination
                    print(f"  终点: {destination}")

            time.sleep(1)  # 避免API调用过于频繁

        self.ai_results = results
        print(f"成功分析 {len(results)} 个文件的终点信息")
        return True
    
    def extract_amount_from_filename(self, filename):
        """从文件名中提取金额"""
        match = re.search(r'-(\d+\.?\d*)-\d{4}年\d{2}月\d{2}日\.pdf$', filename)
        return match.group(1) if match else None
    
    def get_file_mappings(self):
        """获取文件映射关系"""
        pattern = os.path.join(self.current_folder, "行程单-*.pdf")
        itinerary_files = sorted(glob.glob(pattern))
        
        mappings = {}
        for itinerary_file in itinerary_files:
            filename = os.path.basename(itinerary_file)
            amount = self.extract_amount_from_filename(filename)
            
            if amount:
                invoice_pattern = os.path.join(self.current_folder, f"*-{amount}-*.pdf")
                invoice_files = [f for f in glob.glob(invoice_pattern) 
                               if not os.path.basename(f).startswith("行程单-")]
                
                if invoice_files:
                    mappings[amount] = {
                        'itinerary_original': itinerary_file,
                        'invoice_original': invoice_files[0]
                    }
        
        return mappings
    
    def clean_destination_for_filename(self, destination):
        """清理终点名称，生成合适的文件名"""
        clean_destination = re.sub(r'[<>:"/\\|?*]', '_', destination)
        return re.sub(r'\s+', ' ', clean_destination).strip()
    
    def backup_files(self, file_mappings):
        """备份原始文件"""
        backup_choice = input("是否备份原始文件？(回车=不备份, n=备份): ").strip().lower()

        if backup_choice == 'n':
            backup_folder = os.path.join(self.current_folder, "backup_original_files")
            if not os.path.exists(backup_folder):
                os.makedirs(backup_folder)

            print("正在备份原始文件...")
            for files in file_mappings.values():
                shutil.copy2(files['itinerary_original'],
                            os.path.join(backup_folder, os.path.basename(files['itinerary_original'])))
                shutil.copy2(files['invoice_original'],
                            os.path.join(backup_folder, os.path.basename(files['invoice_original'])))

            print("备份完成")
        else:
            print("跳过备份")
    
    def execute_rename(self):
        """执行文件重命名"""
        print("\n开始执行文件重命名...")

        file_mappings = self.get_file_mappings()
        if not file_mappings:
            print("未找到可重命名的文件")
            return False

        self.backup_files(file_mappings)

        success_count = 0
        for amount, files in file_mappings.items():
            if amount in self.ai_results:
                destination = self.ai_results[amount]
                clean_destination = self.clean_destination_for_filename(destination)

                new_itinerary_name = f"{amount}-{clean_destination}-行程单.pdf"
                new_invoice_name = f"{amount}-{clean_destination}-发票.pdf"

                new_itinerary_path = os.path.join(self.current_folder, new_itinerary_name)
                new_invoice_path = os.path.join(self.current_folder, new_invoice_name)

                try:
                    os.rename(files['itinerary_original'], new_itinerary_path)
                    os.rename(files['invoice_original'], new_invoice_path)
                    print(f"重命名成功: {amount} - {clean_destination}")
                    success_count += 2
                except Exception as e:
                    print(f"重命名失败 (金额: {amount}): {str(e)}")

        print(f"重命名完成！成功处理 {success_count} 个文件")
        return True

    def extract_shop_name(self, destination):
        """从终点信息中提取店铺名称"""
        if not destination:
            return "其他地点"

        if '|' in destination:
            parts = destination.split('|')
            if len(parts) >= 2:
                return parts[1].strip()

        return destination.strip()

    def scan_renamed_files(self):
        """扫描已重命名的文件"""
        print("\n扫描已重命名的文件...")

        itinerary_pattern = os.path.join(self.current_folder, "*-行程单.pdf")
        itinerary_files = sorted(glob.glob(itinerary_pattern))

        if not itinerary_files:
            print("未找到已重命名的行程单文件")
            return False

        print(f"找到 {len(itinerary_files)} 个行程单文件")

        for itinerary_file in itinerary_files:
            filename = os.path.basename(itinerary_file)
            match = re.match(r'^(\d+\.?\d*)-(.+)-行程单\.pdf$', filename)

            if match:
                amount = match.group(1)
                destination = match.group(2)
                shop_name = self.extract_shop_name(destination)

                invoice_file = os.path.join(self.current_folder, f"{amount}-{destination}-发票.pdf")

                if os.path.exists(invoice_file):
                    self.shop_data[shop_name].append({
                        'amount': float(amount),
                        'destination': destination,
                        'itinerary_file': itinerary_file,
                        'invoice_file': invoice_file
                    })

        print(f"成功分析 {len(self.shop_data)} 个不同店铺的文件")
        return True

    def create_shop_classification(self):
        """创建店铺归类"""
        print("\n创建店铺归类...")

        classification_folder = os.path.join(self.current_folder, "按店铺归类")
        if not os.path.exists(classification_folder):
            os.makedirs(classification_folder)

        success_count = 0
        files_to_delete = []

        for shop_name, files in self.shop_data.items():
            clean_shop_name = re.sub(r'[<>:"/\\|?*]', '_', shop_name)
            clean_shop_name = re.sub(r'\s+', ' ', clean_shop_name).strip()
            if len(clean_shop_name) > 50:
                clean_shop_name = clean_shop_name[:50] + "..."

            shop_folder = os.path.join(classification_folder, clean_shop_name)

            try:
                if not os.path.exists(shop_folder):
                    os.makedirs(shop_folder)

                for file_info in files:
                    # 移动文件而不是复制
                    itinerary_dest = os.path.join(shop_folder, os.path.basename(file_info['itinerary_file']))
                    invoice_dest = os.path.join(shop_folder, os.path.basename(file_info['invoice_file']))

                    shutil.move(file_info['itinerary_file'], itinerary_dest)
                    shutil.move(file_info['invoice_file'], invoice_dest)

                    files_to_delete.extend([file_info['itinerary_file'], file_info['invoice_file']])

                success_count += 1
                print(f"处理店铺: {shop_name}")

            except Exception as e:
                print(f"处理失败: {shop_name} - {str(e)}")

        # 生成总汇总文件
        self.create_total_summary(classification_folder)

        print(f"店铺归类完成！成功处理 {success_count} 个店铺")
        print("原文件已移动到对应店铺文件夹")
        return True

    def create_total_summary(self, classification_folder):
        """创建总的报销汇总文件"""
        summary_file = os.path.join(classification_folder, "报销总汇总.txt")

        total_amount = sum(sum(file_info['amount'] for file_info in files)
                          for files in self.shop_data.values())
        total_files = sum(len(files) for files in self.shop_data.values())

        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(f"报销总汇总\n")
            f.write(f"=" * 80 + "\n")
            f.write(f"生成时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            f.write(f"总体统计:\n")
            f.write(f"   总店铺数: {len(self.shop_data)} 个\n")
            f.write(f"   总行程数: {total_files} 个\n")
            f.write(f"   总金额: ¥{total_amount:.2f}\n\n")

            f.write(f"按店铺分类汇总:\n")
            f.write(f"=" * 80 + "\n")

            sorted_shops = sorted(self.shop_data.items(),
                                key=lambda x: sum(file_info['amount'] for file_info in x[1]),
                                reverse=True)

            for i, (shop_name, files) in enumerate(sorted_shops, 1):
                shop_amount = sum(file_info['amount'] for file_info in files)
                f.write(f"{i:2d}. 【{shop_name}】\n")
                f.write(f"    行程数量: {len(files)} 个\n")
                f.write(f"    总金额: ¥{shop_amount:.2f}\n")
                f.write(f"    详细清单:\n")

                for file_info in sorted(files, key=lambda x: x['amount']):
                    f.write(f"      - ¥{file_info['amount']:>8.2f} | {file_info['destination']}\n")
                f.write(f"\n")

            f.write(f"=" * 80 + "\n")
            f.write(f"使用说明:\n")
            f.write(f"   1. 每个店铺的文件都在对应的文件夹中\n")
            f.write(f"   2. 可以按店铺文件夹分别提交报销申请\n")
            f.write(f"   3. 每个文件夹包含该店铺的所有行程单和发票\n")
            f.write(f"   4. 原文件已移动到对应店铺文件夹\n")

    def show_menu(self):
        """显示主菜单"""
        print("\n" + "="*60)
        print("PDF智能管理器")
        print("="*60)
        print("1. 完整流程 (提取→分析→重命名→归类)")
        print("2. 仅重命名 (适用于已提取内容的文件)")
        print("3. 仅店铺归类 (适用于已重命名的文件)")
        print("4. 查看文件状态")
        print("0. 退出")
        print("="*60)

    def run(self):
        """运行主程序"""
        print("PDF智能管理器 - 一键脚本")
        print("="*60)

        # 选择文件夹
        pdf_folders = self.find_pdf_folders()
        selected_folder = self.select_folder(pdf_folders)

        if not selected_folder:
            return

        self.current_folder = selected_folder

        while True:
            self.show_menu()
            choice = input("请选择功能 (0-4): ").strip()

            if choice == '0':
                print("再见！")
                break
            elif choice == '1':
                self.full_process()
            elif choice == '2':
                self.rename_only()
            elif choice == '3':
                self.classify_only()
            elif choice == '4':
                self.show_status()
            else:
                print("无效选择，请重新输入")

    def full_process(self):
        """完整流程"""
        print("\n开始完整流程...")

        if not self.extract_pdf_content():
            return

        if not self.analyze_destinations():
            return

        confirm = input("\n确认执行重命名操作？(回车=确认): ")
        if confirm.strip():
            print("操作已取消")
            return

        if not self.execute_rename():
            return

        if not self.scan_renamed_files():
            return

        confirm = input("\n确认执行店铺归类？(回车=确认): ")
        if not confirm.strip():
            self.create_shop_classification()
            print("\n完整流程完成！")
        else:
            print("店铺归类已跳过")

    def rename_only(self):
        """仅重命名"""
        print("\n开始重命名流程...")

        if not self.extract_pdf_content():
            return

        if not self.analyze_destinations():
            return

        confirm = input("\n确认执行重命名操作？(回车=确认): ")
        if not confirm.strip():
            self.execute_rename()
            print("\n重命名完成！")
        else:
            print("操作已取消")

    def classify_only(self):
        """仅店铺归类"""
        print("\n开始店铺归类...")

        if not self.scan_renamed_files():
            return

        confirm = input("\n确认执行店铺归类？(回车=确认): ")
        if not confirm.strip():
            self.create_shop_classification()
            print("\n店铺归类完成！")
        else:
            print("操作已取消")

    def show_status(self):
        """显示文件状态"""
        status = self.check_file_status()

        print(f"\n文件状态报告:")
        print(f"   原始文件: {status['original_count']} 个")
        print(f"   已重命名: {status['renamed_count']} 个")
        print(f"   店铺归类: {'已完成' if status['has_classification'] else '未完成'}")
        print(f"   当前状态: {status['status']}")

def main():
    manager = PDFManager()
    manager.run()

if __name__ == "__main__":
    main()
