#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF智能管理器 - 现代化GUI版本启动脚本
"""

import sys
import tkinter as tk
from tkinter import messagebox
from pathlib import Path
import json

def check_dependencies():
    """检查依赖项"""
    missing_deps = []
    
    try:
        import requests
    except ImportError:
        missing_deps.append("requests")
    
    try:
        import PyPDF2
    except ImportError:
        missing_deps.append("PyPDF2")
    
    if missing_deps:
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror(
            "依赖缺失", 
            f"❌ 缺少以下依赖包：{', '.join(missing_deps)}\n\n"
            f"请运行以下命令安装：\n"
            f"pip install {' '.join(missing_deps)}\n\n"
            f"或者运行：\n"
            f"pip install -r requirements.txt"
        )
        return False
    
    return True

def check_config():
    """检查配置文件"""
    config_file = Path("config.json")
    if not config_file.exists():
        root = tk.Tk()
        root.withdraw()
        result = messagebox.askyesno(
            "配置文件缺失",
            "⚠️ 未找到配置文件 config.json\n\n"
            "是否创建默认配置文件？\n\n"
            "注意：您需要在配置文件中设置API密钥才能使用AI分析功能。"
        )
        
        if result:
            # 创建默认配置
            default_config = {
                "api": {
                    "url": "https://api.siliconflow.cn/v1/chat/completions",
                    "key": "",
                    "model": "deepseek-ai/DeepSeek-V3",
                    "timeout": 60,
                    "max_retries": 3,
                    "retry_delay": 1.0
                },
                "processing": {
                    "max_workers": 4,
                    "batch_size": 10,
                    "enable_parallel": True,
                    "backup_enabled": True
                },
                "ui": {
                    "language": "zh",
                    "theme": "dark",
                    "show_progress": True
                }
            }
            
            try:
                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump(default_config, f, indent=2, ensure_ascii=False)
                
                messagebox.showinfo(
                    "配置创建成功",
                    "✅ 已创建默认配置文件 config.json\n\n"
                    "📝 请编辑配置文件并设置您的API密钥：\n"
                    "   1. 打开 config.json 文件\n"
                    "   2. 在 api.key 字段中填入您的API密钥\n"
                    "   3. 保存文件后重新启动程序\n\n"
                    "💡 提示：没有API密钥也可以使用基础功能（不含AI分析）"
                )
                return True
            except Exception as e:
                messagebox.showerror("错误", f"创建配置文件失败: {str(e)}")
                return False
        else:
            return False
    
    # 检查API密钥
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        api_key = config.get('api', {}).get('key', '')
        if not api_key:
            result = messagebox.askyesno(
                "API密钥未设置",
                "⚠️ 检测到API密钥未设置\n\n"
                "没有API密钥将无法使用AI分析功能，但仍可使用基础功能。\n\n"
                "是否继续启动程序？"
            )
            return result
    except Exception as e:
        messagebox.showwarning("配置警告", f"读取配置文件时出现问题: {str(e)}")
    
    return True

def show_welcome():
    """显示欢迎信息"""
    root = tk.Tk()
    root.withdraw()
    
    welcome_text = """🚀 欢迎使用 PDF智能管理器 v2.0

✨ 现代化特性：
   • 🎨 深色/浅色主题切换
   • 📱 响应式卡片布局  
   • ⚡ 并发处理提升性能
   • 🤖 AI智能分析终点信息
   • 📊 实时统计和进度显示
   • 🔒 文件锁防止冲突
   • 💾 自动备份保护数据

🔧 主要功能：
   • PDF内容提取
   • AI智能分析
   • 文件智能重命名
   • 店铺自动归类

准备好开始了吗？"""
    
    result = messagebox.askyesno("PDF智能管理器 v2.0", welcome_text)
    root.destroy()
    return result

def main():
    """主函数"""
    print("🚀 启动PDF智能管理器现代化GUI版本...")
    
    # 显示欢迎信息
    if not show_welcome():
        print("👋 用户取消启动")
        return
    
    # 检查依赖
    print("🔍 检查依赖项...")
    if not check_dependencies():
        return
    
    # 检查配置
    print("⚙️ 检查配置文件...")
    if not check_config():
        return
    
    try:
        print("🎨 加载现代化界面...")
        # 导入并启动现代化GUI应用
        from modern_gui import ModernPDFManagerGUI
        
        print("✅ 启动成功！")
        app = ModernPDFManagerGUI()
        app.run()
        
    except ImportError as e:
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror(
            "导入错误",
            f"❌ 导入模块失败: {str(e)}\n\n"
            "请确保所有必需的文件都在当前目录中：\n"
            "• modern_gui.py\n"
            "• config.py\n"
            "• pdf_extractor.py\n"
            "• ai_analyzer.py\n"
            "• file_manager.py"
        )
    except Exception as e:
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror(
            "启动错误",
            f"❌ 应用启动失败: {str(e)}\n\n"
            "可能的解决方案：\n"
            "1. 检查配置文件格式是否正确\n"
            "2. 确保所有依赖项已安装\n"
            "3. 检查文件权限\n"
            "4. 重新下载程序文件"
        )

if __name__ == "__main__":
    main()
